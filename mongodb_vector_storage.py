"""
Alternative: MongoDB Atlas Vector Search Implementation
Store embeddings directly in MongoDB Atlas for better integration
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from pymongo import MongoClient
from pymongo.collection import Collection
import numpy as np
from sentence_transformers import SentenceTransformer
from datetime import datetime

logger = logging.getLogger(__name__)

class MongoDBVectorStorage:
    """Store and search embeddings using MongoDB Atlas Vector Search"""
    
    def __init__(self, mongodb_uri: str, vector_db_name: str = "datapilot_vectors"):
        """
        Initialize MongoDB vector storage
        
        Args:
            mongodb_uri: MongoDB connection string
            vector_db_name: Database name for storing vectors
        """
        self.client = MongoClient(mongodb_uri)
        self.vector_db = self.client[vector_db_name]
        self.embeddings_collection = self.vector_db["collection_embeddings"]
        self.metadata_collection = self.vector_db["collection_metadata"]
        
        # Create indexes for better performance
        self._create_indexes()
        
    def _create_indexes(self):
        """Create necessary indexes"""
        try:
            # Index for collection key
            self.embeddings_collection.create_index("collection_key")
            self.metadata_collection.create_index("collection_key")
            
            # Vector search index (requires MongoDB Atlas)
            # This would be created through Atlas UI or API
            logger.info("Indexes created successfully")
        except Exception as e:
            logger.warning(f"Index creation failed: {e}")
    
    def store_collection_embedding(self, collection_key: str, description: str, 
                                 embedding: np.ndarray, metadata: Dict[str, Any]):
        """Store collection embedding and metadata"""
        
        # Store embedding
        embedding_doc = {
            "collection_key": collection_key,
            "description": description,
            "embedding": embedding.tolist(),  # Convert numpy array to list
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        self.embeddings_collection.replace_one(
            {"collection_key": collection_key},
            embedding_doc,
            upsert=True
        )
        
        # Store metadata
        metadata_doc = {
            "collection_key": collection_key,
            "metadata": metadata,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        self.metadata_collection.replace_one(
            {"collection_key": collection_key},
            metadata_doc,
            upsert=True
        )
        
        logger.info(f"Stored embedding for collection: {collection_key}")
    
    def search_similar_collections(self, query_embedding: np.ndarray, 
                                 top_k: int = 5) -> List[Tuple[str, float]]:
        """
        Search for similar collections using vector similarity
        
        Note: This requires MongoDB Atlas Vector Search to be configured
        """
        try:
            # MongoDB Atlas Vector Search aggregation pipeline
            pipeline = [
                {
                    "$vectorSearch": {
                        "index": "collection_embeddings_index",  # Created in Atlas
                        "path": "embedding",
                        "queryVector": query_embedding.tolist(),
                        "numCandidates": top_k * 10,
                        "limit": top_k
                    }
                },
                {
                    "$project": {
                        "collection_key": 1,
                        "description": 1,
                        "score": {"$meta": "vectorSearchScore"}
                    }
                }
            ]
            
            results = list(self.embeddings_collection.aggregate(pipeline))
            
            return [(doc["collection_key"], doc["score"]) for doc in results]
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            # Fallback to cosine similarity calculation
            return self._fallback_similarity_search(query_embedding, top_k)
    
    def _fallback_similarity_search(self, query_embedding: np.ndarray, 
                                  top_k: int) -> List[Tuple[str, float]]:
        """Fallback similarity search using cosine similarity"""
        
        all_embeddings = list(self.embeddings_collection.find())
        similarities = []
        
        for doc in all_embeddings:
            stored_embedding = np.array(doc["embedding"])
            
            # Cosine similarity
            similarity = np.dot(query_embedding, stored_embedding) / (
                np.linalg.norm(query_embedding) * np.linalg.norm(stored_embedding)
            )
            
            similarities.append((doc["collection_key"], float(similarity)))
        
        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def get_collection_metadata(self, collection_key: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific collection"""
        doc = self.metadata_collection.find_one({"collection_key": collection_key})
        return doc["metadata"] if doc else None
    
    def get_all_collections(self) -> List[str]:
        """Get all stored collection keys"""
        return [doc["collection_key"] for doc in self.embeddings_collection.find({}, {"collection_key": 1})]
    
    def clear_cache(self):
        """Clear all stored embeddings and metadata"""
        self.embeddings_collection.delete_many({})
        self.metadata_collection.delete_many({})
        logger.info("Vector cache cleared")

# Example usage configuration for MongoDB Atlas Vector Search
ATLAS_VECTOR_SEARCH_CONFIG = {
    "index_name": "collection_embeddings_index",
    "index_definition": {
        "fields": [
            {
                "type": "vector",
                "path": "embedding",
                "numDimensions": 384,  # For all-MiniLM-L6-v2 model
                "similarity": "cosine"
            }
        ]
    }
}
