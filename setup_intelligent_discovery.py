#!/usr/bin/env python3
"""
Setup script for Intelligent Discovery System
Run this once to analyze your MongoDB database and create embeddings
"""

import os
import sys
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Initialize the intelligent discovery system"""
    
    # Get MongoDB URI from environment
    mongodb_uri = os.getenv('MONGODB_URI')
    if not mongodb_uri:
        logger.error("MONGODB_URI not found in environment variables")
        logger.error("Please add MONGODB_URI to your .env file")
        return False
    
    try:
        # Import and initialize the discovery system
        from intelligent_discovery import IntelligentDiscoverySystem
        
        logger.info("🚀 Initializing Intelligent Discovery System...")
        logger.info(f"📡 Connecting to MongoDB...")
        
        # Initialize the system
        discovery_system = IntelligentDiscoverySystem(mongodb_uri)
        
        logger.info("🔍 Analyzing database schema and creating embeddings...")
        logger.info("⏳ This may take a few minutes depending on your database size...")
        
        # Analyze the database and create embeddings
        discovery_system.analyze_database_schema(max_samples=50)
        
        logger.info("✅ Setup complete!")
        logger.info(f"📊 Analyzed {len(discovery_system.collection_metadata)} collections")
        
        # Show summary
        print("\n" + "="*60)
        print("🎉 INTELLIGENT DISCOVERY SYSTEM READY!")
        print("="*60)
        
        overview = {}
        for collection_key, metadata in discovery_system.collection_metadata.items():
            db_name, coll_name = collection_key.split('.', 1)
            if db_name not in overview:
                overview[db_name] = []
            overview[db_name].append({
                'collection': coll_name,
                'documents': metadata.total_documents,
                'fields': len(metadata.field_types)
            })
        
        for db_name, collections in overview.items():
            print(f"\n📁 Database: {db_name}")
            for coll_info in collections:
                print(f"  📄 {coll_info['collection']}: {coll_info['documents']:,} docs, {coll_info['fields']} fields")
        
        print(f"\n🧠 Embeddings cached for fast query processing")
        print(f"🚀 You can now run: streamlit run app.py")
        print("="*60)
        
        return True
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please make sure all required packages are installed:")
        logger.error("pip install sentence-transformers faiss-cpu scikit-learn")
        return False
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        logger.error("Please check your MongoDB connection and try again")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
