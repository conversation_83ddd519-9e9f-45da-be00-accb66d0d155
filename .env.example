# DataPilot Configuration Example
# Copy this file to .env and configure your settings

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
# For MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# AI Provider API Keys (add at least one for full functionality)
# Get OpenAI API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Get Anthropic API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
DEBUG=true
LOG_LEVEL=INFO
MAX_QUERY_RESULTS=1000
ENABLE_AUTO_VISUALIZATION=true
ENABLE_QUERY_LOGGING=true

# AI Configuration
DEFAULT_AI_MODEL=gpt-3.5-turbo
AI_MAX_TOKENS=2000
AI_TEMPERATURE=0.1

# Security (change in production)
SECRET_KEY=dev-secret-key-change-in-production
ENABLE_AUTHENTICATION=false
JWT_EXPIRATION_HOURS=24

# Performance & Caching
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
# REDIS_URL=redis://localhost:6379

# MongoDB Connection Settings
MONGODB_CONNECTION_TIMEOUT=30000
MONGODB_SERVER_SELECTION_TIMEOUT=30000
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=10

# Visualization Settings
SUPPORTED_CHART_TYPES=bar,line,pie,scatter,histogram,box

# CORS Settings (for API mode)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8501
