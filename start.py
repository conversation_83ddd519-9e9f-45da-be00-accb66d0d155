#!/usr/bin/env python3
"""
DataPilot Startup Script
Easy way to start the DataPilot application with proper setup
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print the DataPilot banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🚀 DataPilot v1.0                         ║
    ║              Intelligent MongoDB Query System                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'streamlit',
        'pymongo', 
        'pandas',
        'plotly',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please run:")
            print(f"   pip install {' '.join(missing_packages)}")
            return False
    
    return True

def check_environment():
    """Check environment configuration"""
    print("\n🔧 Checking environment configuration...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️  .env file not found. Creating default configuration...")
        create_default_env()
    
    # Load and check environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check MongoDB URI
    mongodb_uri = os.getenv('MONGODB_URI')
    if mongodb_uri:
        print(f"   ✅ MongoDB URI configured")
    else:
        print("   ⚠️  MongoDB URI not configured (will use demo mode)")
    
    # Check AI API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    
    if openai_key:
        print("   ✅ OpenAI API key configured")
    elif anthropic_key:
        print("   ✅ Anthropic API key configured")
    else:
        print("   ⚠️  No AI API keys configured (limited functionality)")
    
    return True

def create_default_env():
    """Create a default .env file"""
    default_env = """# DataPilot Configuration

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
# MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# AI Provider API Keys (add at least one for full functionality)
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
DEBUG=true
LOG_LEVEL=INFO
MAX_QUERY_RESULTS=1000
ENABLE_AUTO_VISUALIZATION=true

# Security
SECRET_KEY=dev-secret-key-change-in-production
ENABLE_AUTHENTICATION=false

# Performance
CACHE_ENABLED=true
# REDIS_URL=redis://localhost:6379
"""
    
    with open('.env', 'w') as f:
        f.write(default_env)
    
    print("   ✅ Created default .env file")
    print("   📝 Edit .env to configure your MongoDB URI and API keys")

def test_imports():
    """Test if our modules can be imported"""
    print("\n🧪 Testing module imports...")
    
    try:
        from utils.config import config
        print("   ✅ Configuration module")
    except Exception as e:
        print(f"   ❌ Configuration module: {e}")
        return False
    
    try:
        from core.mongodb_manager import MongoDBManager
        print("   ✅ MongoDB Manager")
    except Exception as e:
        print(f"   ❌ MongoDB Manager: {e}")
        return False
    
    try:
        from core.query_engine import IntelligentQueryEngine
        print("   ✅ Query Engine")
    except Exception as e:
        print(f"   ❌ Query Engine: {e}")
        return False
    
    try:
        from core.visualization_engine import VisualizationEngine
        print("   ✅ Visualization Engine")
    except Exception as e:
        print(f"   ❌ Visualization Engine: {e}")
        return False
    
    try:
        from ui.components import UIComponents
        print("   ✅ UI Components")
    except Exception as e:
        print(f"   ❌ UI Components: {e}")
        return False
    
    return True

def start_application():
    """Start the Streamlit application"""
    print("\n🚀 Starting DataPilot...")
    print("📱 The application will open in your default browser")
    print("🌐 URL: http://localhost:8501")
    print("\n💡 Tips:")
    print("   - Try the demo mode if you don't have MongoDB connected")
    print("   - Add your API keys to .env for full AI functionality")
    print("   - Use Ctrl+C to stop the application")
    
    print("\n" + "="*60)
    
    try:
        # Start Streamlit
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'app.py',
            '--server.port=8501',
            '--server.address=localhost'
        ])
    except KeyboardInterrupt:
        print("\n\n👋 DataPilot stopped. Thank you for using our system!")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("💡 Try running manually: streamlit run app.py")

def main():
    """Main startup function"""
    print_banner()
    
    # Check if we're in the right directory
    if not Path('app.py').exists():
        print("❌ app.py not found. Please run this script from the DataPilot directory.")
        sys.exit(1)
    
    # Run all checks
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages.")
        sys.exit(1)
    
    if not check_environment():
        print("❌ Environment check failed. Please check your configuration.")
        sys.exit(1)
    
    if not test_imports():
        print("❌ Module import test failed. Please check your installation.")
        sys.exit(1)
    
    print("\n✅ All checks passed! Ready to start DataPilot.")
    
    # Ask user if they want to continue
    try:
        response = input("\n🚀 Start DataPilot now? (y/n): ").lower().strip()
        if response in ['y', 'yes', '']:
            start_application()
        else:
            print("👋 Startup cancelled. Run 'python start.py' when ready!")
    except KeyboardInterrupt:
        print("\n👋 Startup cancelled.")

if __name__ == "__main__":
    main()
