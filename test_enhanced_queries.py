#!/usr/bin/env python3
"""
Test Enhanced MongoDB Queries
Test the new intelligent query processing capabilities
"""

import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

def test_enhanced_queries():
    """Test the enhanced query processing"""
    print("🧪 Testing Enhanced MongoDB Query Processing...")
    
    try:
        from core.mongodb_manager import MongoDBManager
        from app import execute_simple_mongodb_query, find_relevant_collection
        
        # Initialize MongoDB manager
        manager = MongoDBManager()
        print("✅ MongoDB Manager initialized")
        
        # Connect to MongoDB
        success = manager.connect_sync()
        
        if success:
            print("✅ Connected to MongoDB")
            print(f"📊 Current database: {manager.current_database}")
            
            # Get collections
            collections = manager.get_collections()
            print(f"📁 Available collections: {collections}")
            
            # Test queries
            test_queries = [
                "what is the most costlier product?",
                "show me all products",
                "count total documents",
                "list all collections",
                "what is the cheapest item?",
                "show me some data"
            ]
            
            print("\n🔍 Testing Enhanced Queries:")
            print("=" * 50)
            
            for query in test_queries:
                print(f"\n🤔 Query: '{query}'")
                try:
                    result = execute_simple_mongodb_query(query, manager)
                    print(f"✅ Result: {result[:200]}...")
                except Exception as e:
                    print(f"❌ Error: {e}")
            
            # Test collection finding
            print("\n🎯 Testing Collection Finding:")
            print("=" * 50)
            
            test_collection_queries = [
                ("find products", collections),
                ("show users", collections),
                ("get reviews", collections),
                ("list characters", collections)
            ]
            
            for query, colls in test_collection_queries:
                try:
                    relevant = find_relevant_collection(query, colls)
                    print(f"Query: '{query}' → Collection: '{relevant}'")
                except Exception as e:
                    print(f"❌ Error finding collection for '{query}': {e}")
            
            # Disconnect
            manager.disconnect()
            print("\n🔌 Disconnected from MongoDB")
            
            return True
            
        else:
            print("❌ Failed to connect to MongoDB")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_query_analysis():
    """Test query analysis functions"""
    print("\n🔬 Testing Query Analysis Functions...")
    
    try:
        from app import find_relevant_collection
        
        # Test collection finding logic
        test_collections = ['products', 'users', 'orders', 'reviews', 'comics_character', 'images']
        
        test_cases = [
            ("most expensive product", "products"),
            ("show all users", "users"),
            ("count orders", "orders"),
            ("find reviews", "reviews"),
            ("character information", "comics_character"),
            ("show images", "images")
        ]
        
        print("Testing collection relevance scoring:")
        for query, expected in test_cases:
            result = find_relevant_collection(query.lower(), test_collections)
            status = "✅" if expected in result.lower() else "⚠️"
            print(f"{status} '{query}' → '{result}' (expected: {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ Query analysis test failed: {e}")
        return False

def main():
    """Run all enhanced query tests"""
    print("🚀 DataPilot Enhanced Query Testing")
    print("=" * 60)
    
    # Test query analysis
    analysis_ok = test_query_analysis()
    
    # Test enhanced queries (requires MongoDB connection)
    queries_ok = test_enhanced_queries()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Enhanced Query Test Summary:")
    print(f"   Query Analysis: {'✅ Pass' if analysis_ok else '❌ Fail'}")
    print(f"   MongoDB Queries: {'✅ Pass' if queries_ok else '❌ Fail'}")
    
    if analysis_ok and queries_ok:
        print("\n🎉 All enhanced query tests passed!")
        print("💡 Your DataPilot system now has intelligent query processing!")
        print("🔥 Try asking: 'What is the most expensive product?' in the web interface")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
