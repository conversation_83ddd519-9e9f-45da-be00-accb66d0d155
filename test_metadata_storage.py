"""
Test script for Metadata Storage & Vector Indexing Engine
Demonstrates schema storage, vector embeddings, and intelligent retrieval
"""

import asyncio
import json
from datetime import datetime
from mongodb_connection_manager import EnhancedMongoConnectionManager
from schema_discovery_engine import SchemaDiscoveryEngine
from metadata_storage_engine import MetadataStorageEngine

async def test_metadata_storage():
    """Test the complete metadata storage and retrieval pipeline"""
    
    print("🗄️ Testing Metadata Storage & Vector Indexing Engine")
    print("=" * 70)
    
    # Initialize all components
    connection_manager = EnhancedMongoConnectionManager()
    
    try:
        # Connect to MongoDB
        print("\n1️⃣ Connecting to MongoDB...")
        connected = await connection_manager.connect()
        
        if not connected:
            print("❌ Failed to connect to MongoDB")
            return
        
        # Initialize engines
        print("\n2️⃣ Initializing Schema Discovery and Metadata Storage...")
        schema_engine = SchemaDiscoveryEngine(connection_manager)
        metadata_engine = MetadataStorageEngine("test_metadata_storage")
        
        # Discover and store schemas for key collections
        test_collections = [
            ("sample_mflix", "movies"),
            ("sample_mflix", "users"),
            ("comics-generator", "products"),
            ("database_assistant", "chat_messages")
        ]
        
        print("\n3️⃣ Discovering and Storing Schemas...")
        print("-" * 50)
        
        stored_schemas = []
        for db_name, collection_name in test_collections:
            # Check if collection exists
            available_collections = connection_manager.get_collection_list(db_name)
            if collection_name not in available_collections:
                print(f"⚠️  Collection {db_name}.{collection_name} not found, skipping...")
                continue
            
            print(f"\n🔍 Processing: {db_name}.{collection_name}")
            
            try:
                # Discover schema
                schema = await schema_engine.discover_collection_schema(
                    db_name, collection_name, sample_size=50
                )
                
                # Store schema with embeddings
                schema_id = metadata_engine.store_schema(schema)
                stored_schemas.append(schema_id)
                
                print(f"   ✅ Stored schema: {schema_id}")
                print(f"   📊 Fields: {len(schema.fields)}, Confidence: {schema.schema_confidence:.2f}")
                
            except Exception as e:
                print(f"   ❌ Error processing {db_name}.{collection_name}: {str(e)}")
        
        # Test storage statistics
        print("\n4️⃣ Storage Statistics:")
        print("-" * 50)
        stats = metadata_engine.get_storage_stats()
        for key, value in stats.items():
            print(f"   • {key}: {value}")
        
        # Test semantic search
        print("\n5️⃣ Testing Semantic Search...")
        print("-" * 50)
        
        test_queries = [
            "find movies with high ratings",
            "show user information and emails",
            "product details and pricing",
            "chat messages and conversations",
            "movie titles and genres",
            "user accounts and profiles"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            
            # Find relevant schemas
            relevant_schemas = metadata_engine.find_relevant_schemas(query, limit=3)
            print(f"   📋 Relevant Collections:")
            for schema_id, similarity in relevant_schemas:
                print(f"      • {schema_id} (similarity: {similarity:.3f})")
            
            # Find relevant fields
            schema_ids = [s[0] for s in relevant_schemas]
            relevant_fields = metadata_engine.find_relevant_fields(query, schema_ids, limit=5)
            print(f"   🔑 Relevant Fields:")
            for schema_id, field_name, similarity in relevant_fields:
                print(f"      • {schema_id}.{field_name} (similarity: {similarity:.3f})")
        
        # Test field suggestions
        print("\n6️⃣ Testing Field Suggestions...")
        print("-" * 50)
        
        test_partials = ["name", "email", "rating", "price", "title", "id"]
        
        for partial in test_partials:
            suggestions = metadata_engine.get_field_suggestions(partial, limit=5)
            if suggestions:
                print(f"\n🔍 Fields containing '{partial}':")
                for suggestion in suggestions:
                    print(f"   • {suggestion['schema_id']}.{suggestion['field_name']}")
        
        # Test schema retrieval
        print("\n7️⃣ Testing Schema Retrieval...")
        print("-" * 50)
        
        if stored_schemas:
            test_schema_id = stored_schemas[0]
            retrieved_schema = metadata_engine.get_schema_by_id(test_schema_id)
            if retrieved_schema:
                print(f"✅ Successfully retrieved schema: {test_schema_id}")
                if isinstance(retrieved_schema, dict):
                    print(f"   Database: {retrieved_schema.get('database', 'unknown')}")
                    print(f"   Collection: {retrieved_schema.get('collection', 'unknown')}")
                    print(f"   Fields: {len(retrieved_schema.get('fields', {}))}")
            else:
                print(f"❌ Failed to retrieve schema: {test_schema_id}")
        
    except Exception as e:
        print(f"❌ Error during metadata storage testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        connection_manager.close_connection()
        print("\n✅ Metadata storage testing completed")

async def test_advanced_vector_search():
    """Test advanced vector search capabilities"""
    
    print("\n" + "=" * 70)
    print("🚀 Testing Advanced Vector Search")
    print("=" * 70)
    
    metadata_engine = MetadataStorageEngine("test_metadata_storage")
    
    try:
        # Test complex queries
        complex_queries = [
            "movies with actors and directors information",
            "user authentication and login data",
            "product catalog with prices and ratings",
            "chat history and message content",
            "movie reviews and ratings from critics",
            "user preferences and viewing history"
        ]
        
        print("\n🧠 Complex Query Analysis:")
        print("-" * 50)
        
        for query in complex_queries:
            print(f"\n🔍 Complex Query: '{query}'")
            
            # Find relevant schemas
            relevant_schemas = metadata_engine.find_relevant_schemas(query, limit=2)
            
            if relevant_schemas:
                print(f"   📋 Best Matches:")
                for schema_id, similarity in relevant_schemas:
                    print(f"      • {schema_id} (confidence: {similarity:.3f})")
                    
                    # Get detailed field matches
                    relevant_fields = metadata_engine.find_relevant_fields(
                        query, [schema_id], limit=3
                    )
                    for _, field_name, field_similarity in relevant_fields:
                        print(f"        - {field_name} ({field_similarity:.3f})")
            else:
                print("   ❌ No relevant schemas found")
        
        # Test vocabulary analysis
        print(f"\n📚 Vocabulary Analysis:")
        print(f"   Total vocabulary size: {len(metadata_engine.vocabulary)}")
        print(f"   Sample words: {list(metadata_engine.vocabulary)[:10]}")
        
        # Test embedding cache
        print(f"\n🧠 Embedding Cache Analysis:")
        collection_embeddings = sum(1 for e in metadata_engine.embeddings_cache.values() 
                                   if e.element_type == "collection")
        field_embeddings = sum(1 for e in metadata_engine.embeddings_cache.values() 
                              if e.element_type == "field")
        
        print(f"   Collection embeddings: {collection_embeddings}")
        print(f"   Field embeddings: {field_embeddings}")
        print(f"   Total embeddings: {len(metadata_engine.embeddings_cache)}")
        
    except Exception as e:
        print(f"❌ Error during advanced vector search testing: {str(e)}")

def run_metadata_tests():
    """Run all metadata storage tests"""
    print("🧪 Starting Metadata Storage & Vector Indexing Tests")
    print(f"⏰ Started at: {datetime.now()}")
    
    # Run basic metadata storage tests
    asyncio.run(test_metadata_storage())
    
    # Run advanced vector search tests
    asyncio.run(test_advanced_vector_search())
    
    print(f"\n⏰ Completed at: {datetime.now()}")
    print("🎉 All metadata storage tests completed!")

if __name__ == "__main__":
    run_metadata_tests()
