"""
Visualization Engine for DataPilot
Automatically generates charts and visualizations from MongoDB query results
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
from dataclasses import dataclass
from enum import Enum

# Visualization imports
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("Plotly not available. Visualization features will be limited.")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from utils.config import config

logger = logging.getLogger(__name__)

class ChartType(Enum):
    """Supported chart types"""
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    SCATTER = "scatter"
    HISTOGRAM = "histogram"
    BOX = "box"
    HEATMAP = "heatmap"
    AREA = "area"

@dataclass
class VisualizationConfig:
    """Configuration for visualization generation"""
    chart_type: ChartType
    title: str
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None
    color_column: Optional[str] = None
    size_column: Optional[str] = None
    facet_column: Optional[str] = None
    height: int = 500
    width: Optional[int] = None
    theme: str = "plotly"

class VisualizationEngine:
    """
    Intelligent visualization engine that:
    1. Analyzes query results to determine best chart type
    2. Automatically selects appropriate columns for visualization
    3. Generates interactive charts using Plotly
    4. Provides fallback options when data is not suitable for visualization
    """
    
    def __init__(self):
        """Initialize the visualization engine"""
        self.available = PLOTLY_AVAILABLE
        
        # Chart type detection patterns
        self.chart_patterns = {
            ChartType.PIE: ['category', 'type', 'status', 'group'],
            ChartType.BAR: ['count', 'total', 'sum', 'average'],
            ChartType.LINE: ['date', 'time', 'month', 'year', 'trend'],
            ChartType.SCATTER: ['correlation', 'relationship', 'vs'],
            ChartType.HISTOGRAM: ['distribution', 'frequency']
        }
        
        # Color schemes
        self.color_schemes = {
            'default': 'Blues',
            'categorical': 'Set3',
            'sequential': 'Viridis',
            'diverging': 'RdBu'
        }
        
        logger.info(f"Visualization Engine initialized (Plotly available: {self.available})")
    
    def create_visualization(self, data: List[Dict[str, Any]], 
                           query_context: str = "",
                           chart_type: Optional[ChartType] = None) -> Optional[go.Figure]:
        """
        Create visualization from query results
        
        Args:
            data: Query results as list of dictionaries
            query_context: Original query for context
            chart_type: Specific chart type (auto-detected if None)
            
        Returns:
            Plotly figure or None if visualization not possible
        """
        if not self.available or not data:
            logger.warning("Visualization not available or no data provided")
            return None
        
        try:
            # Convert to DataFrame
            df = self._prepare_dataframe(data)
            if df.empty:
                logger.warning("No valid data for visualization")
                return None
            
            # Auto-detect chart type if not specified
            if chart_type is None:
                chart_type = self._detect_chart_type(df, query_context)
            
            # Generate visualization configuration
            viz_config = self._generate_viz_config(df, chart_type, query_context)
            
            # Create the chart
            fig = self._create_chart(df, viz_config)
            
            if fig:
                # Apply styling and enhancements
                self._enhance_chart(fig, viz_config)
                logger.info(f"Created {chart_type.value} chart with {len(df)} data points")
            
            return fig
            
        except Exception as e:
            logger.error(f"Error creating visualization: {str(e)}")
            return None
    
    def _prepare_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """Prepare and clean data for visualization"""
        try:
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            if df.empty:
                return df
            
            # Remove MongoDB-specific fields
            columns_to_remove = [col for col in df.columns if col.startswith('_') and col != '_id']
            df = df.drop(columns=columns_to_remove, errors='ignore')
            
            # Handle _id field specially
            if '_id' in df.columns:
                # Convert ObjectId to string if needed
                df['_id'] = df['_id'].astype(str)
                # If _id is the only identifier, keep it, otherwise remove
                if len(df.columns) > 1:
                    df = df.drop(columns=['_id'], errors='ignore')
            
            # Clean and convert data types
            df = self._clean_dataframe(df)
            
            # Limit data size for performance
            if len(df) > 1000:
                logger.info(f"Limiting visualization data to 1000 rows (from {len(df)})")
                df = df.head(1000)
            
            return df
            
        except Exception as e:
            logger.error(f"Error preparing dataframe: {str(e)}")
            return pd.DataFrame()
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and optimize dataframe for visualization"""
        try:
            # Handle missing values
            df = df.fillna('N/A')
            
            # Convert numeric strings to numbers
            for col in df.columns:
                if df[col].dtype == 'object':
                    # Try to convert to numeric
                    numeric_series = pd.to_numeric(df[col], errors='coerce')
                    if not numeric_series.isna().all():
                        # If more than 50% of values are numeric, convert the column
                        if numeric_series.notna().sum() / len(df) > 0.5:
                            df[col] = numeric_series
            
            # Handle datetime columns
            for col in df.columns:
                if 'date' in col.lower() or 'time' in col.lower():
                    try:
                        df[col] = pd.to_datetime(df[col], errors='ignore')
                    except:
                        pass
            
            # Remove rows where all values are NaN
            df = df.dropna(how='all')
            
            return df
            
        except Exception as e:
            logger.error(f"Error cleaning dataframe: {str(e)}")
            return df
    
    def _detect_chart_type(self, df: pd.DataFrame, query_context: str) -> ChartType:
        """Automatically detect the best chart type for the data"""
        try:
            query_lower = query_context.lower()
            
            # Check for explicit chart type requests in query
            if any(word in query_lower for word in ['pie', 'donut']):
                return ChartType.PIE
            elif any(word in query_lower for word in ['line', 'trend', 'over time']):
                return ChartType.LINE
            elif any(word in query_lower for word in ['scatter', 'correlation']):
                return ChartType.SCATTER
            elif any(word in query_lower for word in ['histogram', 'distribution']):
                return ChartType.HISTOGRAM
            
            # Analyze data structure
            numeric_cols = df.select_dtypes(include=['number']).columns
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns
            datetime_cols = df.select_dtypes(include=['datetime']).columns
            
            # Decision logic based on data structure
            if len(categorical_cols) == 1 and len(numeric_cols) == 1:
                # One categorical, one numeric - good for bar or pie
                unique_categories = df[categorical_cols[0]].nunique()
                if unique_categories <= 10:
                    return ChartType.PIE if unique_categories <= 6 else ChartType.BAR
                else:
                    return ChartType.BAR
            
            elif len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
                # Time series data - line chart
                return ChartType.LINE
            
            elif len(numeric_cols) >= 2:
                # Multiple numeric columns - scatter plot
                return ChartType.SCATTER
            
            elif len(numeric_cols) == 1:
                # Single numeric column - histogram
                return ChartType.HISTOGRAM
            
            else:
                # Default to bar chart
                return ChartType.BAR
                
        except Exception as e:
            logger.error(f"Error detecting chart type: {str(e)}")
            return ChartType.BAR
    
    def _generate_viz_config(self, df: pd.DataFrame, chart_type: ChartType, 
                           query_context: str) -> VisualizationConfig:
        """Generate visualization configuration based on data and chart type"""
        try:
            # Get column types
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            datetime_cols = df.select_dtypes(include=['datetime']).columns.tolist()
            
            # Generate title
            title = self._generate_title(query_context, chart_type)
            
            # Select columns based on chart type
            x_axis, y_axis, color_column = self._select_columns(
                df, chart_type, numeric_cols, categorical_cols, datetime_cols
            )
            
            return VisualizationConfig(
                chart_type=chart_type,
                title=title,
                x_axis=x_axis,
                y_axis=y_axis,
                color_column=color_column,
                height=500
            )
            
        except Exception as e:
            logger.error(f"Error generating viz config: {str(e)}")
            return VisualizationConfig(
                chart_type=ChartType.BAR,
                title="Data Visualization",
                height=500
            )
    
    def _generate_title(self, query_context: str, chart_type: ChartType) -> str:
        """Generate appropriate title for the chart"""
        if query_context:
            # Extract meaningful parts from query
            words = query_context.split()
            if len(words) > 10:
                title = ' '.join(words[:10]) + "..."
            else:
                title = query_context
            return title.title()
        else:
            return f"{chart_type.value.title()} Chart"
    
    def _select_columns(self, df: pd.DataFrame, chart_type: ChartType,
                       numeric_cols: List[str], categorical_cols: List[str],
                       datetime_cols: List[str]) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Select appropriate columns for x-axis, y-axis, and color"""
        x_axis = None
        y_axis = None
        color_column = None
        
        try:
            if chart_type == ChartType.BAR:
                # Categorical x-axis, numeric y-axis
                x_axis = categorical_cols[0] if categorical_cols else None
                y_axis = numeric_cols[0] if numeric_cols else None
                
            elif chart_type == ChartType.LINE:
                # Datetime/categorical x-axis, numeric y-axis
                x_axis = datetime_cols[0] if datetime_cols else (categorical_cols[0] if categorical_cols else None)
                y_axis = numeric_cols[0] if numeric_cols else None
                
            elif chart_type == ChartType.PIE:
                # Use categorical for labels, numeric for values
                x_axis = categorical_cols[0] if categorical_cols else None
                y_axis = numeric_cols[0] if numeric_cols else None
                
            elif chart_type == ChartType.SCATTER:
                # Two numeric columns
                x_axis = numeric_cols[0] if len(numeric_cols) > 0 else None
                y_axis = numeric_cols[1] if len(numeric_cols) > 1 else numeric_cols[0]
                color_column = categorical_cols[0] if categorical_cols else None
                
            elif chart_type == ChartType.HISTOGRAM:
                # Single numeric column
                x_axis = numeric_cols[0] if numeric_cols else None
                
            # Fallback: use first available columns
            if not x_axis and df.columns.tolist():
                x_axis = df.columns[0]
            if not y_axis and len(df.columns) > 1:
                y_axis = df.columns[1]
                
        except Exception as e:
            logger.error(f"Error selecting columns: {str(e)}")
        
        return x_axis, y_axis, color_column

    def _create_chart(self, df: pd.DataFrame, config: VisualizationConfig) -> Optional[go.Figure]:
        """Create the actual chart based on configuration"""
        try:
            if config.chart_type == ChartType.BAR:
                return self._create_bar_chart(df, config)
            elif config.chart_type == ChartType.LINE:
                return self._create_line_chart(df, config)
            elif config.chart_type == ChartType.PIE:
                return self._create_pie_chart(df, config)
            elif config.chart_type == ChartType.SCATTER:
                return self._create_scatter_chart(df, config)
            elif config.chart_type == ChartType.HISTOGRAM:
                return self._create_histogram(df, config)
            else:
                # Default to bar chart
                return self._create_bar_chart(df, config)

        except Exception as e:
            logger.error(f"Error creating {config.chart_type.value} chart: {str(e)}")
            return None

    def _create_bar_chart(self, df: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """Create a bar chart"""
        try:
            if config.x_axis and config.y_axis:
                # Standard bar chart with x and y
                fig = px.bar(
                    df,
                    x=config.x_axis,
                    y=config.y_axis,
                    color=config.color_column,
                    title=config.title,
                    height=config.height
                )
            elif config.x_axis:
                # Value counts bar chart
                value_counts = df[config.x_axis].value_counts().head(20)
                fig = px.bar(
                    x=value_counts.index,
                    y=value_counts.values,
                    title=config.title,
                    height=config.height
                )
                fig.update_xaxes(title=config.x_axis)
                fig.update_yaxes(title="Count")
            else:
                # Fallback: use first column
                col = df.columns[0]
                value_counts = df[col].value_counts().head(20)
                fig = px.bar(
                    x=value_counts.index,
                    y=value_counts.values,
                    title=config.title,
                    height=config.height
                )

            return fig

        except Exception as e:
            logger.error(f"Error creating bar chart: {str(e)}")
            return None

    def _create_line_chart(self, df: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """Create a line chart"""
        try:
            if config.x_axis and config.y_axis:
                fig = px.line(
                    df,
                    x=config.x_axis,
                    y=config.y_axis,
                    color=config.color_column,
                    title=config.title,
                    height=config.height
                )
            else:
                # Use index as x-axis
                y_col = config.y_axis or df.select_dtypes(include=['number']).columns[0]
                fig = px.line(
                    x=df.index,
                    y=df[y_col],
                    title=config.title,
                    height=config.height
                )
                fig.update_xaxes(title="Index")
                fig.update_yaxes(title=y_col)

            return fig

        except Exception as e:
            logger.error(f"Error creating line chart: {str(e)}")
            return None

    def _create_pie_chart(self, df: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """Create a pie chart"""
        try:
            if config.x_axis and config.y_axis:
                # Use specified columns
                fig = px.pie(
                    df,
                    names=config.x_axis,
                    values=config.y_axis,
                    title=config.title,
                    height=config.height
                )
            elif config.x_axis:
                # Value counts pie chart
                value_counts = df[config.x_axis].value_counts().head(10)
                fig = px.pie(
                    names=value_counts.index,
                    values=value_counts.values,
                    title=config.title,
                    height=config.height
                )
            else:
                # Use first categorical column
                categorical_cols = df.select_dtypes(include=['object', 'category']).columns
                if len(categorical_cols) > 0:
                    col = categorical_cols[0]
                    value_counts = df[col].value_counts().head(10)
                    fig = px.pie(
                        names=value_counts.index,
                        values=value_counts.values,
                        title=config.title,
                        height=config.height
                    )
                else:
                    return None

            return fig

        except Exception as e:
            logger.error(f"Error creating pie chart: {str(e)}")
            return None

    def _create_scatter_chart(self, df: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """Create a scatter plot"""
        try:
            numeric_cols = df.select_dtypes(include=['number']).columns

            if len(numeric_cols) >= 2:
                x_col = config.x_axis or numeric_cols[0]
                y_col = config.y_axis or numeric_cols[1]

                fig = px.scatter(
                    df,
                    x=x_col,
                    y=y_col,
                    color=config.color_column,
                    size=config.size_column,
                    title=config.title,
                    height=config.height
                )
            else:
                # Not enough numeric columns for scatter plot
                return None

            return fig

        except Exception as e:
            logger.error(f"Error creating scatter chart: {str(e)}")
            return None

    def _create_histogram(self, df: pd.DataFrame, config: VisualizationConfig) -> go.Figure:
        """Create a histogram"""
        try:
            numeric_cols = df.select_dtypes(include=['number']).columns

            if len(numeric_cols) > 0:
                col = config.x_axis or numeric_cols[0]

                fig = px.histogram(
                    df,
                    x=col,
                    color=config.color_column,
                    title=config.title,
                    height=config.height,
                    nbins=30
                )
            else:
                return None

            return fig

        except Exception as e:
            logger.error(f"Error creating histogram: {str(e)}")
            return None

    def _enhance_chart(self, fig: go.Figure, config: VisualizationConfig):
        """Apply styling and enhancements to the chart"""
        try:
            # Update layout for better appearance
            fig.update_layout(
                title_font_size=16,
                title_x=0.5,  # Center title
                showlegend=True,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)',
                font=dict(size=12),
                margin=dict(l=50, r=50, t=80, b=50)
            )

            # Update axes
            fig.update_xaxes(
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linewidth=1,
                linecolor='rgba(128,128,128,0.5)'
            )

            fig.update_yaxes(
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linewidth=1,
                linecolor='rgba(128,128,128,0.5)'
            )

            # Add hover effects
            fig.update_traces(
                hovertemplate='<b>%{x}</b><br>%{y}<extra></extra>'
            )

        except Exception as e:
            logger.error(f"Error enhancing chart: {str(e)}")

    def get_chart_suggestions(self, data: List[Dict[str, Any]]) -> List[str]:
        """Get suggestions for chart types based on data structure"""
        if not data:
            return []

        try:
            df = pd.DataFrame(data)
            numeric_cols = df.select_dtypes(include=['number']).columns
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns
            datetime_cols = df.select_dtypes(include=['datetime']).columns

            suggestions = []

            if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
                suggestions.extend(["Bar Chart", "Pie Chart"])

            if len(datetime_cols) >= 1 and len(numeric_cols) >= 1:
                suggestions.append("Line Chart")

            if len(numeric_cols) >= 2:
                suggestions.append("Scatter Plot")

            if len(numeric_cols) >= 1:
                suggestions.append("Histogram")

            return suggestions

        except Exception as e:
            logger.error(f"Error getting chart suggestions: {str(e)}")
            return ["Bar Chart", "Line Chart", "Pie Chart"]
