"""
Schema Discovery Engine for DataPilot
Intelligently discovers and analyzes MongoDB database schemas
"""

import logging
from typing import Dict, Any, List, Optional, Set, Tuple
import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import json
import re
from collections import defaultdict, Counter

from utils.config import config

logger = logging.getLogger(__name__)

@dataclass
class FieldInfo:
    """Information about a database field"""
    name: str
    data_type: str
    sample_values: List[Any] = field(default_factory=list)
    null_count: int = 0
    unique_count: int = 0
    is_indexed: bool = False
    semantic_type: Optional[str] = None  # e.g., 'email', 'phone', 'date', 'price'
    description: Optional[str] = None

@dataclass
class CollectionSchema:
    """Schema information for a MongoDB collection"""
    database: str
    collection: str
    document_count: int
    sample_size: int
    fields: Dict[str, FieldInfo] = field(default_factory=dict)
    relationships: List[str] = field(default_factory=list)
    indexes: List[str] = field(default_factory=list)
    business_context: Optional[str] = None
    confidence_score: float = 0.0
    last_analyzed: Optional[datetime] = None

@dataclass
class DatabaseSchema:
    """Complete schema information for a database"""
    name: str
    collections: Dict[str, CollectionSchema] = field(default_factory=dict)
    relationships: List[Tuple[str, str, str]] = field(default_factory=list)  # (collection1, collection2, relationship_type)
    business_domain: Optional[str] = None
    last_updated: Optional[datetime] = None

class SchemaDiscoveryEngine:
    """
    Intelligent schema discovery engine that:
    1. Analyzes collection structures and field types
    2. Identifies semantic meaning of fields
    3. Discovers relationships between collections
    4. Provides business context understanding
    5. Caches schema information for performance
    """
    
    def __init__(self):
        """Initialize the schema discovery engine"""
        self.discovered_schemas: Dict[str, DatabaseSchema] = {}
        self.field_patterns = self._initialize_field_patterns()
        self.business_patterns = self._initialize_business_patterns()
        
        logger.info("Schema Discovery Engine initialized")
    
    def _initialize_field_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for semantic field type detection"""
        return {
            'email': [r'.*email.*', r'.*mail.*', r'.*@.*'],
            'phone': [r'.*phone.*', r'.*tel.*', r'.*mobile.*', r'\+?\d{10,}'],
            'url': [r'.*url.*', r'.*link.*', r'https?://.*'],
            'date': [r'.*date.*', r'.*time.*', r'.*created.*', r'.*updated.*'],
            'price': [r'.*price.*', r'.*cost.*', r'.*amount.*', r'.*value.*'],
            'name': [r'.*name.*', r'.*title.*', r'.*label.*'],
            'id': [r'.*id$', r'.*_id$', r'.*identifier.*'],
            'status': [r'.*status.*', r'.*state.*', r'.*condition.*'],
            'category': [r'.*category.*', r'.*type.*', r'.*kind.*', r'.*class.*'],
            'description': [r'.*desc.*', r'.*comment.*', r'.*note.*', r'.*detail.*'],
            'address': [r'.*address.*', r'.*location.*', r'.*city.*', r'.*country.*'],
            'quantity': [r'.*qty.*', r'.*quantity.*', r'.*count.*', r'.*number.*']
        }
    
    def _initialize_business_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for business domain detection"""
        return {
            'ecommerce': ['products', 'orders', 'customers', 'cart', 'inventory', 'sales'],
            'blog': ['posts', 'articles', 'comments', 'authors', 'categories', 'tags'],
            'crm': ['contacts', 'leads', 'opportunities', 'accounts', 'deals'],
            'hr': ['employees', 'departments', 'positions', 'payroll', 'attendance'],
            'finance': ['transactions', 'accounts', 'invoices', 'payments', 'budgets'],
            'social': ['users', 'posts', 'likes', 'follows', 'messages', 'friends'],
            'education': ['students', 'courses', 'grades', 'teachers', 'classes'],
            'healthcare': ['patients', 'doctors', 'appointments', 'treatments', 'records']
        }
    
    async def discover_database_schema(self, mongodb_manager, database_name: str) -> DatabaseSchema:
        """
        Discover complete schema for a database
        
        Args:
            mongodb_manager: MongoDB manager instance
            database_name: Name of database to analyze
            
        Returns:
            DatabaseSchema object with complete analysis
        """
        try:
            logger.info(f"Discovering schema for database: {database_name}")
            
            # Check if already cached
            if database_name in self.discovered_schemas:
                cached_schema = self.discovered_schemas[database_name]
                if cached_schema.last_updated and \
                   (datetime.now() - cached_schema.last_updated).seconds < 3600:  # 1 hour cache
                    logger.info(f"Using cached schema for {database_name}")
                    return cached_schema
            
            # Get collections in the database
            collections = mongodb_manager.get_collections(database_name)
            
            # Create database schema object
            db_schema = DatabaseSchema(
                name=database_name,
                last_updated=datetime.now()
            )
            
            # Analyze each collection
            for collection_name in collections:
                try:
                    collection_schema = await self._discover_collection_schema(
                        mongodb_manager, database_name, collection_name
                    )
                    db_schema.collections[collection_name] = collection_schema
                except Exception as e:
                    logger.error(f"Error analyzing collection {collection_name}: {str(e)}")
            
            # Discover relationships between collections
            db_schema.relationships = self._discover_relationships(db_schema)
            
            # Determine business domain
            db_schema.business_domain = self._determine_business_domain(db_schema)
            
            # Cache the result
            self.discovered_schemas[database_name] = db_schema
            
            logger.info(f"Schema discovery complete for {database_name}: {len(db_schema.collections)} collections")
            return db_schema
            
        except Exception as e:
            logger.error(f"Error discovering database schema: {str(e)}")
            return DatabaseSchema(name=database_name)
    
    async def _discover_collection_schema(self, mongodb_manager, database_name: str, 
                                        collection_name: str) -> CollectionSchema:
        """Discover schema for a specific collection"""
        try:
            # Get collection info from MongoDB manager
            collection_info = mongodb_manager.get_collection_info(collection_name, database_name)
            
            if not collection_info:
                return CollectionSchema(
                    database=database_name,
                    collection=collection_name,
                    document_count=0,
                    sample_size=0
                )
            
            # Create collection schema
            schema = CollectionSchema(
                database=database_name,
                collection=collection_name,
                document_count=collection_info.document_count,
                sample_size=1,  # We have one sample document
                indexes=collection_info.indexes or [],
                last_analyzed=datetime.now()
            )
            
            # Analyze fields from sample document
            if collection_info.sample_document:
                schema.fields = self._analyze_document_fields(collection_info.sample_document)
                schema.sample_size = 1
            
            # Determine business context
            schema.business_context = self._determine_collection_context(collection_name, schema.fields)
            
            # Calculate confidence score
            schema.confidence_score = self._calculate_confidence_score(schema)
            
            return schema
            
        except Exception as e:
            logger.error(f"Error discovering collection schema for {collection_name}: {str(e)}")
            return CollectionSchema(
                database=database_name,
                collection=collection_name,
                document_count=0,
                sample_size=0
            )
    
    def _analyze_document_fields(self, document: Dict[str, Any]) -> Dict[str, FieldInfo]:
        """Analyze fields in a document to extract schema information"""
        fields = {}
        
        try:
            for field_name, value in document.items():
                field_info = FieldInfo(
                    name=field_name,
                    data_type=self._get_data_type(value),
                    sample_values=[value] if value is not None else [],
                    semantic_type=self._detect_semantic_type(field_name, value)
                )
                
                fields[field_name] = field_info
            
        except Exception as e:
            logger.error(f"Error analyzing document fields: {str(e)}")
        
        return fields
    
    def _get_data_type(self, value: Any) -> str:
        """Determine the data type of a value"""
        if value is None:
            return "null"
        elif isinstance(value, bool):
            return "boolean"
        elif isinstance(value, int):
            return "integer"
        elif isinstance(value, float):
            return "float"
        elif isinstance(value, str):
            return "string"
        elif isinstance(value, list):
            return "array"
        elif isinstance(value, dict):
            return "object"
        elif hasattr(value, '__class__'):
            return value.__class__.__name__
        else:
            return "unknown"
    
    def _detect_semantic_type(self, field_name: str, value: Any) -> Optional[str]:
        """Detect the semantic type of a field based on name and value"""
        field_name_lower = field_name.lower()
        
        # Check field name patterns
        for semantic_type, patterns in self.field_patterns.items():
            for pattern in patterns:
                if re.search(pattern, field_name_lower, re.IGNORECASE):
                    return semantic_type
        
        # Check value patterns for strings
        if isinstance(value, str):
            if '@' in value and '.' in value:
                return 'email'
            elif value.startswith(('http://', 'https://')):
                return 'url'
            elif re.match(r'^\+?\d{10,}$', value):
                return 'phone'
        
        return None
    
    def _determine_collection_context(self, collection_name: str, fields: Dict[str, FieldInfo]) -> str:
        """Determine the business context of a collection"""
        collection_lower = collection_name.lower()
        
        # Check collection name patterns
        context_indicators = {
            'user': ['users', 'customers', 'accounts', 'profiles'],
            'product': ['products', 'items', 'inventory', 'catalog'],
            'order': ['orders', 'purchases', 'transactions', 'sales'],
            'content': ['posts', 'articles', 'blogs', 'content'],
            'analytics': ['events', 'logs', 'metrics', 'analytics']
        }
        
        for context, indicators in context_indicators.items():
            if any(indicator in collection_lower for indicator in indicators):
                return context
        
        # Check field patterns
        field_names = [field.lower() for field in fields.keys()]
        if any('price' in field or 'cost' in field for field in field_names):
            return 'product'
        elif any('email' in field for field in field_names):
            return 'user'
        elif any('order' in field for field in field_names):
            return 'order'
        
        return 'general'
    
    def _discover_relationships(self, db_schema: DatabaseSchema) -> List[Tuple[str, str, str]]:
        """Discover relationships between collections"""
        relationships = []
        
        try:
            collections = list(db_schema.collections.keys())
            
            # Look for foreign key relationships
            for i, coll1 in enumerate(collections):
                for j, coll2 in enumerate(collections):
                    if i != j:
                        relationship = self._find_relationship(
                            db_schema.collections[coll1],
                            db_schema.collections[coll2]
                        )
                        if relationship:
                            relationships.append((coll1, coll2, relationship))
            
        except Exception as e:
            logger.error(f"Error discovering relationships: {str(e)}")
        
        return relationships
    
    def _find_relationship(self, schema1: CollectionSchema, schema2: CollectionSchema) -> Optional[str]:
        """Find relationship between two collections"""
        # Look for foreign key patterns
        for field_name, field_info in schema1.fields.items():
            if field_info.semantic_type == 'id':
                # Check if this ID field references the other collection
                if schema2.collection.lower() in field_name.lower():
                    return 'references'
        
        return None
    
    def _determine_business_domain(self, db_schema: DatabaseSchema) -> str:
        """Determine the overall business domain of the database"""
        collection_names = [name.lower() for name in db_schema.collections.keys()]
        
        # Score each business domain
        domain_scores = {}
        for domain, indicators in self.business_patterns.items():
            score = sum(1 for indicator in indicators if any(indicator in name for name in collection_names))
            if score > 0:
                domain_scores[domain] = score
        
        # Return the domain with highest score
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        
        return 'general'
    
    def _calculate_confidence_score(self, schema: CollectionSchema) -> float:
        """Calculate confidence score for schema analysis"""
        score = 0.0
        
        # Base score for having data
        if schema.document_count > 0:
            score += 0.3
        
        # Score for field analysis
        if schema.fields:
            score += 0.3
            
            # Bonus for semantic type detection
            semantic_fields = sum(1 for field in schema.fields.values() if field.semantic_type)
            if semantic_fields > 0:
                score += 0.2 * (semantic_fields / len(schema.fields))
        
        # Score for business context
        if schema.business_context and schema.business_context != 'general':
            score += 0.2
        
        return min(score, 1.0)
    
    def get_schema_summary(self, database_name: str) -> str:
        """Get a formatted summary of the database schema"""
        if database_name not in self.discovered_schemas:
            return f"No schema information available for database: {database_name}"
        
        schema = self.discovered_schemas[database_name]
        
        summary = f"📊 Database: {schema.name}\n"
        summary += f"🏢 Business Domain: {schema.business_domain or 'Unknown'}\n"
        summary += f"📁 Collections: {len(schema.collections)}\n\n"
        
        for collection_name, collection_schema in schema.collections.items():
            summary += f"📦 {collection_name} ({collection_schema.document_count} documents)\n"
            summary += f"   Context: {collection_schema.business_context}\n"
            summary += f"   Confidence: {collection_schema.confidence_score:.2f}\n"
            
            if collection_schema.fields:
                field_summary = []
                for field_name, field_info in list(collection_schema.fields.items())[:5]:  # Show first 5 fields
                    semantic_info = f" ({field_info.semantic_type})" if field_info.semantic_type else ""
                    field_summary.append(f"{field_name}{semantic_info}")
                summary += f"   Fields: {', '.join(field_summary)}\n"
            
            summary += "\n"
        
        return summary
    
    def find_relevant_collections(self, query: str, database_name: str) -> List[Tuple[str, float]]:
        """Find collections relevant to a query"""
        if database_name not in self.discovered_schemas:
            return []
        
        schema = self.discovered_schemas[database_name]
        query_lower = query.lower()
        
        relevance_scores = []
        
        for collection_name, collection_schema in schema.collections.items():
            score = 0.0
            
            # Check collection name relevance
            if collection_name.lower() in query_lower:
                score += 1.0
            
            # Check field name relevance
            for field_name, field_info in collection_schema.fields.items():
                if field_name.lower() in query_lower:
                    score += 0.5
                
                # Check semantic type relevance
                if field_info.semantic_type and field_info.semantic_type in query_lower:
                    score += 0.3
            
            # Check business context relevance
            if collection_schema.business_context and collection_schema.business_context in query_lower:
                score += 0.4
            
            if score > 0:
                relevance_scores.append((collection_name, score))
        
        # Sort by relevance score
        relevance_scores.sort(key=lambda x: x[1], reverse=True)
        
        return relevance_scores[:5]  # Return top 5 relevant collections
