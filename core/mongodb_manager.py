"""
MongoDB Connection and Management System
Handles connections, database operations, and cluster discovery
"""

import pymongo
from pymongo import MongoClient
from typing import Dict, List, Any, Optional, Tuple
import logging
import asyncio
from dataclasses import dataclass
from datetime import datetime
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
import json

from utils.config import config

logger = logging.getLogger(__name__)

@dataclass
class DatabaseInfo:
    """Information about a database"""
    name: str
    collections: List[str]
    size_bytes: Optional[int] = None
    document_count: Optional[int] = None
    last_updated: Optional[datetime] = None

@dataclass
class CollectionInfo:
    """Information about a collection"""
    database: str
    name: str
    document_count: int
    size_bytes: Optional[int] = None
    indexes: List[str] = None
    sample_document: Optional[Dict[str, Any]] = None
    schema_fields: List[str] = None

class MongoDBManager:
    """
    MongoDB connection and management system that handles:
    - Connection management with retry logic
    - Database and collection discovery
    - Query execution with performance monitoring
    - Schema analysis and caching
    """
    
    def __init__(self):
        """Initialize MongoDB manager"""
        self.client: Optional[MongoClient] = None
        self.current_database: Optional[str] = None
        self.is_connected: bool = False
        self.connection_info: Dict[str, Any] = {}
        
        # Caching
        self.database_cache: Dict[str, DatabaseInfo] = {}
        self.collection_cache: Dict[str, CollectionInfo] = {}
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.query_stats: Dict[str, Any] = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'avg_response_time': 0.0
        }
        
        logger.info("MongoDB Manager initialized")
    
    async def connect(self, uri: Optional[str] = None) -> bool:
        """
        Connect to MongoDB with retry logic
        
        Args:
            uri: MongoDB connection URI (uses config if not provided)
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            connection_uri = uri or config.get_mongodb_uri()
            
            logger.info("Attempting to connect to MongoDB...")
            
            # Create client with optimized settings
            self.client = MongoClient(
                connection_uri,
                serverSelectionTimeoutMS=config.database.server_selection_timeout,
                connectTimeoutMS=config.database.connection_timeout,
                maxPoolSize=config.database.max_pool_size,
                minPoolSize=config.database.min_pool_size,
                maxIdleTimeMS=config.database.max_idle_time,
                retryWrites=config.database.retry_writes
            )
            
            # Test connection
            await self._test_connection()
            
            # Discover cluster information
            await self._discover_cluster()
            
            self.is_connected = True
            logger.info("✅ Successfully connected to MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to MongoDB: {str(e)}")
            self.is_connected = False
            return False
    
    async def _test_connection(self):
        """Test MongoDB connection with ping"""
        try:
            # Use asyncio to run the blocking ping command
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                await loop.run_in_executor(
                    executor, 
                    self.client.admin.command, 
                    'ping'
                )
            logger.info("MongoDB ping successful")
        except Exception as e:
            raise ConnectionError(f"MongoDB connection test failed: {str(e)}")
    
    async def _discover_cluster(self):
        """Discover databases and collections in the cluster"""
        try:
            logger.info("Discovering MongoDB cluster structure...")
            
            # Get database names
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                db_names = await loop.run_in_executor(
                    executor,
                    self.client.list_database_names
                )
            
            # Filter out system databases
            user_databases = [
                db for db in db_names 
                if db not in ['admin', 'local', 'config']
            ]
            
            # Discover each database
            for db_name in user_databases:
                await self._discover_database(db_name)
            
            # Set current database if not set
            if not self.current_database and user_databases:
                self.current_database = user_databases[0]
            
            self.connection_info = {
                'total_databases': len(user_databases),
                'user_databases': user_databases,
                'current_database': self.current_database,
                'connected_at': datetime.now().isoformat()
            }
            
            logger.info(f"Discovered {len(user_databases)} databases")
            
        except Exception as e:
            logger.error(f"Error discovering cluster: {str(e)}")
    
    async def _discover_database(self, db_name: str):
        """Discover collections in a specific database"""
        try:
            db = self.client[db_name]
            
            # Get collection names
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                collection_names = await loop.run_in_executor(
                    executor,
                    db.list_collection_names
                )
            
            # Create database info
            db_info = DatabaseInfo(
                name=db_name,
                collections=collection_names,
                last_updated=datetime.now()
            )
            
            self.database_cache[db_name] = db_info
            
            # Discover each collection (limit to avoid long startup times)
            for collection_name in collection_names[:10]:  # Limit to first 10 collections
                await self._discover_collection(db_name, collection_name)
            
            logger.info(f"Discovered database '{db_name}' with {len(collection_names)} collections")
            
        except Exception as e:
            logger.error(f"Error discovering database {db_name}: {str(e)}")
    
    async def _discover_collection(self, db_name: str, collection_name: str):
        """Discover schema and metadata for a specific collection"""
        try:
            db = self.client[db_name]
            collection = db[collection_name]
            
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                # Get document count
                doc_count = await loop.run_in_executor(
                    executor,
                    collection.estimated_document_count
                )
                
                # Get sample document
                sample_doc = None
                if doc_count > 0:
                    sample_doc = await loop.run_in_executor(
                        executor,
                        collection.find_one
                    )
                
                # Get indexes
                indexes = await loop.run_in_executor(
                    executor,
                    lambda: list(collection.list_indexes())
                )
            
            # Extract schema fields from sample document
            schema_fields = []
            if sample_doc:
                schema_fields = list(sample_doc.keys())
            
            # Create collection info
            collection_info = CollectionInfo(
                database=db_name,
                name=collection_name,
                document_count=doc_count,
                sample_document=sample_doc,
                schema_fields=schema_fields,
                indexes=[idx.get('name', 'unknown') for idx in indexes]
            )
            
            collection_key = f"{db_name}.{collection_name}"
            self.collection_cache[collection_key] = collection_info
            
        except Exception as e:
            logger.error(f"Error discovering collection {db_name}.{collection_name}: {str(e)}")
    
    def get_databases(self) -> List[str]:
        """Get list of available databases"""
        if not self.is_connected:
            return []
        return list(self.database_cache.keys())
    
    def get_collections(self, database: Optional[str] = None) -> List[str]:
        """Get list of collections in a database"""
        db_name = database or self.current_database
        if not db_name or db_name not in self.database_cache:
            return []
        return self.database_cache[db_name].collections
    
    def get_collection_info(self, collection: str, database: Optional[str] = None) -> Optional[CollectionInfo]:
        """Get detailed information about a collection"""
        db_name = database or self.current_database
        collection_key = f"{db_name}.{collection}"
        return self.collection_cache.get(collection_key)
    
    def switch_database(self, database_name: str) -> bool:
        """Switch to a different database"""
        if database_name in self.database_cache:
            self.current_database = database_name
            logger.info(f"Switched to database: {database_name}")
            return True
        else:
            logger.error(f"Database not found: {database_name}")
            return False
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status and information"""
        return {
            'connected': self.is_connected,
            'current_database': self.current_database,
            'connection_info': self.connection_info,
            'query_stats': self.query_stats,
            'cached_databases': len(self.database_cache),
            'cached_collections': len(self.collection_cache)
        }
    
    async def execute_query(self, query: str, database: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute a MongoDB query and return results

        Args:
            query: MongoDB query string (PyMongo format)
            database: Database name (uses current if not specified)

        Returns:
            Dict containing query results, metadata, and performance info
        """
        if not self.is_connected:
            return {
                'success': False,
                'error': 'Not connected to MongoDB',
                'results': [],
                'execution_time': 0
            }

        start_time = time.time()
        db_name = database or self.current_database

        try:
            # Update query stats
            self.query_stats['total_queries'] += 1

            # Get database reference
            db = self.client[db_name]

            # Execute query in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                # Create safe execution environment
                safe_globals = {
                    'db': db,
                    'client': self.client,
                    'list': list,
                    'json': json,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'dict': dict,
                    'set': set,
                    'tuple': tuple,
                    'range': range,
                    '__builtins__': {
                        'range': range,
                        'len': len,
                        'str': str,
                        'int': int,
                        'float': float,
                        'list': list,
                        'dict': dict,
                        'sum': sum,
                        'max': max,
                        'min': min,
                        'sorted': sorted,
                        'enumerate': enumerate,
                        'zip': zip,
                    }
                }

                # Execute query
                result = await loop.run_in_executor(
                    executor,
                    lambda: eval(query, safe_globals, {})
                )

            # Process results
            processed_result = self._process_query_result(result)
            execution_time = time.time() - start_time

            # Update stats
            self.query_stats['successful_queries'] += 1
            self._update_avg_response_time(execution_time)

            return {
                'success': True,
                'results': processed_result,
                'execution_time': execution_time,
                'database': db_name,
                'query': query,
                'result_count': len(processed_result) if isinstance(processed_result, list) else 1
            }

        except Exception as e:
            execution_time = time.time() - start_time
            self.query_stats['failed_queries'] += 1

            error_msg = str(e)
            logger.error(f"Query execution failed: {error_msg}")

            return {
                'success': False,
                'error': error_msg,
                'results': [],
                'execution_time': execution_time,
                'database': db_name,
                'query': query
            }

    def _process_query_result(self, result: Any) -> Any:
        """Process and format query results"""
        try:
            if isinstance(result, list):
                # Convert ObjectIds and other non-serializable types to strings
                return [self._serialize_document(doc) for doc in result]
            elif hasattr(result, 'inserted_ids'):
                # Handle insert operations
                return f"Successfully inserted {len(result.inserted_ids)} documents"
            elif hasattr(result, 'modified_count'):
                # Handle update operations
                return f"Successfully updated {result.modified_count} documents"
            elif hasattr(result, 'deleted_count'):
                # Handle delete operations
                return f"Successfully deleted {result.deleted_count} documents"
            elif isinstance(result, (str, int, float, bool)):
                return result
            else:
                # For other types, try to serialize
                return self._serialize_document(result)

        except Exception as e:
            logger.error(f"Error processing query result: {str(e)}")
            return str(result)

    def _serialize_document(self, doc: Any) -> Any:
        """Serialize MongoDB documents for JSON compatibility"""
        if isinstance(doc, dict):
            return {
                key: self._serialize_document(value)
                for key, value in doc.items()
            }
        elif isinstance(doc, list):
            return [self._serialize_document(item) for item in doc]
        elif hasattr(doc, '__dict__'):
            # Handle MongoDB objects like ObjectId
            return str(doc)
        else:
            return doc

    def _update_avg_response_time(self, execution_time: float):
        """Update average response time statistics"""
        total_queries = self.query_stats['successful_queries']
        if total_queries == 1:
            self.query_stats['avg_response_time'] = execution_time
        else:
            current_avg = self.query_stats['avg_response_time']
            new_avg = ((current_avg * (total_queries - 1)) + execution_time) / total_queries
            self.query_stats['avg_response_time'] = round(new_avg, 4)

    def get_schema_summary(self, database: Optional[str] = None) -> str:
        """Get a formatted summary of database schema"""
        db_name = database or self.current_database
        if not db_name or db_name not in self.database_cache:
            return "No database information available"

        db_info = self.database_cache[db_name]
        summary = f"📊 Database: {db_name}\n"
        summary += f"📁 Collections: {len(db_info.collections)}\n\n"

        for collection_name in db_info.collections[:10]:  # Limit to 10 collections
            collection_key = f"{db_name}.{collection_name}"
            if collection_key in self.collection_cache:
                coll_info = self.collection_cache[collection_key]
                summary += f"📦 {collection_name} ({coll_info.document_count} documents)\n"

                if coll_info.schema_fields:
                    fields = coll_info.schema_fields[:8]  # Show first 8 fields
                    summary += f"   Fields: {', '.join(fields)}\n"

                if coll_info.sample_document:
                    # Show sample values
                    sample_values = {}
                    for field in fields:
                        if field in coll_info.sample_document:
                            value = coll_info.sample_document[field]
                            if isinstance(value, dict):
                                sample_values[field] = "{object}"
                            elif isinstance(value, list):
                                sample_values[field] = f"[{len(value)} items]"
                            else:
                                sample_values[field] = str(value)[:30]
                    summary += f"   Sample: {sample_values}\n"

                summary += "\n"

        return summary

    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            self.client = None
            self.is_connected = False
            self.current_database = None
            logger.info("Disconnected from MongoDB")
