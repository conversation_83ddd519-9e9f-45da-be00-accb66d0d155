"""
Advanced Intelligent Query Processor for Natural Language Understanding
Focuses on generating natural, user-friendly responses without technical details
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import re
from datetime import datetime
from pymongo import MongoClient
from .schema_discovery import SchemaDiscoveryEngine

logger = logging.getLogger(__name__)

class IntelligentQueryProcessor:
    def __init__(self, mongo_client: MongoClient):
        self.mongo_client = mongo_client
        self.schema_discovery = SchemaDiscoveryEngine()
        
        # Advanced intent recognition with natural response templates
        self.intent_patterns = {
            'count_query': {
                'patterns': [
                    r'how many\s+(.+?)(?:\s+are there|\s+do we have|\s+exist|$)',
                    r'count\s+(?:all\s+)?(.+?)(?:\s+in|\s+from|$)',
                    r'total\s+(?:number\s+of\s+)?(.+?)(?:\s+in|\s+from|$)',
                    r'number\s+of\s+(.+?)(?:\s+in|\s+from|$)'
                ],
                'response_templates': [
                    "I found {count} {entity} in your database.",
                    "There are {count} {entity} total.",
                    "Your database contains {count} {entity}."
                ]
            },
            'list_query': {
                'patterns': [
                    r'(?:list|show|display|get)\s+(?:all\s+)?(?:the\s+)?(.+?)(?:\s+from|\s+in|$)',
                    r'what\s+(?:are\s+)?(?:all\s+)?(?:the\s+)?(.+?)(?:\s+available|\s+in|\s+from|$)',
                    r'give\s+me\s+(?:all\s+)?(?:the\s+)?(.+?)(?:\s+from|\s+in|$)'
                ],
                'response_templates': [
                    "Here are the {entity} I found:",
                    "I found these {entity} for you:",
                    "Here's your list of {entity}:"
                ]
            },
            'filter_query': {
                'patterns': [
                    r'find\s+(.+?)\s+(?:where|with|that|having)\s+(.+)',
                    r'show\s+me\s+(.+?)\s+(?:where|with|that|having)\s+(.+)',
                    r'get\s+(.+?)\s+(?:where|with|that|having)\s+(.+)',
                    r'(.+?)\s+(?:where|with|that|having)\s+(.+)'
                ],
                'response_templates': [
                    "Here are the {entity} matching your criteria:",
                    "I found these {entity} that match what you're looking for:",
                    "Based on your filters, here are the {entity}:"
                ]
            },
            'superlative_query': {
                'patterns': [
                    r'(?:what\s+is\s+the\s+)?(?:most\s+expensive|highest\s+priced|costliest)\s+(.+)',
                    r'(?:what\s+is\s+the\s+)?(?:cheapest|lowest\s+priced|least\s+expensive)\s+(.+)',
                    r'(?:what\s+is\s+the\s+)?(?:highest|maximum|max)\s+(.+)',
                    r'(?:what\s+is\s+the\s+)?(?:lowest|minimum|min)\s+(.+)'
                ],
                'response_templates': [
                    "The {adjective} {entity} is:",
                    "Here's the {adjective} {entity} I found:",
                    "Based on your criteria, the {adjective} {entity} is:"
                ]
            }
        }
        
        # Smart field mapping for better understanding
        self.field_mapping = {
            'price': ['price', 'cost', 'amount', 'value', 'pricing', 'rate', 'fee', 'charge'],
            'name': ['name', 'title', 'label', 'product_name', 'item_name', 'full_name', 'display_name'],
            'age': ['age', 'years', 'old', 'birth_year', 'born', 'age_years'],
            'email': ['email', 'mail', 'email_address', 'contact_email', 'e_mail'],
            'phone': ['phone', 'mobile', 'contact_number', 'telephone', 'cell', 'phone_number'],
            'date': ['date', 'time', 'created', 'updated', 'timestamp', 'created_at', 'modified'],
            'status': ['status', 'state', 'condition', 'active', 'enabled', 'is_active'],
            'category': ['category', 'type', 'kind', 'genre', 'classification', 'group'],
            'rating': ['rating', 'score', 'stars', 'review', 'feedback', 'rating_score'],
            'quantity': ['quantity', 'count', 'amount', 'stock', 'inventory', 'qty', 'available']
        }
    
    def process_query(self, user_query: str, selected_database: str) -> Dict[str, Any]:
        """
        Process user query with advanced intelligence and natural responses
        
        Args:
            user_query: Natural language query from user
            selected_database: Selected database name
            
        Returns:
            Dictionary with natural response and results
        """
        try:
            logger.info(f"Processing intelligent query: {user_query}")
            
            # Step 1: Analyze query intent and extract entities
            intent_analysis = self._analyze_query_intent(user_query)
            
            # Step 2: Find relevant collections
            relevant_collections = self._find_relevant_collections(
                user_query, selected_database, intent_analysis
            )
            
            if not relevant_collections:
                return {
                    'success': False,
                    'user_query': user_query,
                    'response': "I couldn't find any relevant data for your question. Please try rephrasing your query or check if you're connected to the right database.",
                    'results': [],
                    'result_count': 0,
                    'confidence_score': 0.0,
                    'error': 'No relevant collections found'
                }
            
            # Step 3: Execute intelligent query
            best_collection = relevant_collections[0]
            query_result = self._execute_intelligent_query(
                user_query, best_collection, intent_analysis, selected_database
            )
            
            return query_result
            
        except Exception as e:
            logger.error(f"Error in intelligent query processing: {str(e)}")
            return {
                'success': False,
                'user_query': user_query,
                'response': f"I encountered an error while processing your query: {str(e)}",
                'results': [],
                'result_count': 0,
                'confidence_score': 0.0,
                'error': str(e)
            }
    
    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """Analyze query intent using advanced pattern matching"""
        query_lower = query.lower().strip()
        
        for intent_type, config in self.intent_patterns.items():
            for pattern in config['patterns']:
                match = re.search(pattern, query_lower)
                if match:
                    entity = self._extract_entity(match.group(1) if match.groups() else query_lower)
                    conditions = self._extract_conditions(query_lower)
                    
                    return {
                        'intent': intent_type,
                        'entity': entity,
                        'conditions': conditions,
                        'confidence': 0.8,
                        'matched_pattern': pattern,
                        'response_templates': config['response_templates']
                    }
        
        # Default intent
        return {
            'intent': 'general_query',
            'entity': self._extract_entity(query_lower),
            'conditions': self._extract_conditions(query_lower),
            'confidence': 0.5,
            'matched_pattern': None,
            'response_templates': ["Here's what I found for your query:"]
        }
    
    def _extract_entity(self, text: str) -> str:
        """Extract the main entity from query text"""
        # Remove common stop words and extract meaningful nouns
        stop_words = {'the', 'all', 'some', 'any', 'a', 'an', 'is', 'are', 'was', 'were', 'have', 'has', 'do', 'does'}
        words = [word for word in text.split() if word not in stop_words]
        
        # Look for plural nouns (likely entities)
        for word in words:
            if word.endswith('s') and len(word) > 3:
                return word
        
        # Look for known entity types
        entity_keywords = {
            'products': ['product', 'item', 'goods'],
            'customers': ['customer', 'client', 'user'],
            'orders': ['order', 'purchase', 'transaction'],
            'sales': ['sale', 'revenue', 'income'],
            'categories': ['category', 'type', 'kind'],
            'reviews': ['review', 'rating', 'feedback']
        }
        
        for entity, keywords in entity_keywords.items():
            if any(keyword in text for keyword in keywords):
                return entity
        
        # Fallback: use first meaningful word
        return words[0] if words else 'items'
    
    def _extract_conditions(self, query: str) -> Dict[str, Any]:
        """Extract filter conditions from query"""
        conditions = {}
        
        # Price conditions
        price_patterns = [
            r'(?:price|cost|amount)\s*(?:>|greater than|more than|above)\s*(\d+)',
            r'(?:price|cost|amount)\s*(?:<|less than|below|under)\s*(\d+)',
            r'(?:price|cost|amount)\s*(?:=|equals?|is)\s*(\d+)',
            r'expensive|costly|high.?priced',
            r'cheap|inexpensive|low.?priced'
        ]
        
        for pattern in price_patterns:
            match = re.search(pattern, query)
            if match and match.groups():
                value = int(match.group(1))
                if 'greater' in pattern or '>' in pattern:
                    conditions['price'] = {'$gt': value}
                elif 'less' in pattern or '<' in pattern:
                    conditions['price'] = {'$lt': value}
                elif 'equals' in pattern or '=' in pattern:
                    conditions['price'] = value
            elif 'expensive' in query or 'costly' in query:
                conditions['_sort'] = {'price': -1}
            elif 'cheap' in query or 'inexpensive' in query:
                conditions['_sort'] = {'price': 1}
        
        # Status conditions
        if 'active' in query:
            conditions['status'] = 'active'
        elif 'inactive' in query:
            conditions['status'] = 'inactive'
        
        return conditions
    
    def _find_relevant_collections(self, query: str, database: str, intent_analysis: Dict) -> List[str]:
        """Find relevant collections based on query and intent"""
        try:
            db = self.mongo_client[database]
            collections = db.list_collection_names()
            
            entity = intent_analysis['entity']
            query_lower = query.lower()
            
            # Score collections based on relevance
            scored_collections = []
            
            for collection in collections:
                score = 0
                collection_lower = collection.lower()
                
                # Direct entity match
                if entity in collection_lower or collection_lower in entity:
                    score += 10
                
                # Partial matches
                entity_words = entity.split()
                for word in entity_words:
                    if word in collection_lower:
                        score += 5
                
                # Query keyword matches
                query_words = query_lower.split()
                for word in query_words:
                    if len(word) > 3 and word in collection_lower:
                        score += 3
                
                if score > 0:
                    scored_collections.append((collection, score))
            
            # Sort by score and return top collections
            scored_collections.sort(key=lambda x: x[1], reverse=True)
            return [col[0] for col in scored_collections[:3]]
            
        except Exception as e:
            logger.error(f"Error finding relevant collections: {e}")
            return []
    
    def _execute_intelligent_query(self, query: str, collection: str, intent_analysis: Dict, database: str) -> Dict[str, Any]:
        """Execute the query and generate natural response"""
        try:
            db = self.mongo_client[database]
            coll = db[collection]
            
            intent = intent_analysis['intent']
            conditions = intent_analysis['conditions']
            entity = intent_analysis['entity']
            
            # Build MongoDB query based on intent
            mongo_filter = {k: v for k, v in conditions.items() if not k.startswith('_')}
            sort_criteria = conditions.get('_sort', {})
            
            start_time = datetime.now()
            
            if intent == 'count_query':
                result_count = coll.count_documents(mongo_filter)
                results = []
                execution_time = (datetime.now() - start_time).total_seconds()
                
                response = f"I found {result_count} {entity} in your database."
                
            elif intent == 'superlative_query':
                # Find highest/lowest based on query
                if sort_criteria:
                    results = list(coll.find(mongo_filter).sort(list(sort_criteria.items())).limit(1))
                else:
                    # Default to price sorting for superlatives
                    price_field = self._find_price_field(coll)
                    if price_field:
                        sort_direction = -1 if 'expensive' in query or 'highest' in query else 1
                        results = list(coll.find(mongo_filter).sort(price_field, sort_direction).limit(1))
                    else:
                        results = list(coll.find(mongo_filter).limit(1))
                
                result_count = len(results)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if results:
                    item = results[0]
                    adjective = 'most expensive' if 'expensive' in query else 'top'
                    response = f"The {adjective} {entity[:-1] if entity.endswith('s') else entity} is:"
                else:
                    response = f"I couldn't find any {entity} matching your criteria."
                    
            else:
                # List/filter queries
                cursor = coll.find(mongo_filter)
                if sort_criteria:
                    cursor = cursor.sort(list(sort_criteria.items()))
                
                results = list(cursor.limit(50))  # Limit for performance
                result_count = len(results)
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if result_count == 0:
                    response = f"I couldn't find any {entity} matching your criteria."
                elif result_count == 1:
                    response = f"I found 1 {entity[:-1] if entity.endswith('s') else entity}:"
                else:
                    response = f"I found {result_count} {entity}:"
            
            return {
                'success': True,
                'user_query': query,
                'response': response,
                'results': results,
                'result_count': result_count,
                'execution_time': execution_time,
                'confidence_score': intent_analysis['confidence'],
                'collection_name': collection,
                'database_name': database,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"Error executing intelligent query: {e}")
            return {
                'success': False,
                'user_query': query,
                'response': f"I encountered an error while searching: {str(e)}",
                'results': [],
                'result_count': 0,
                'execution_time': 0,
                'confidence_score': 0.0,
                'collection_name': collection,
                'database_name': database,
                'error': str(e)
            }
    
    def _find_price_field(self, collection) -> Optional[str]:
        """Find the price field in a collection"""
        try:
            sample = collection.find_one()
            if not sample:
                return None
            
            for field_name in sample.keys():
                field_lower = field_name.lower()
                if any(price_word in field_lower for price_word in ['price', 'cost', 'amount', 'value']):
                    return field_name
            
            return None
        except:
            return None
