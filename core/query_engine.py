"""
Enhanced Intelligent Query Engine for Natural Language to MongoDB Query Conversion
Uses RAG-based intelligent discovery system for semantic understanding
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import re
import json
from dataclasses import dataclass
from enum import Enum
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our enhanced query processor
try:
    from enhanced_query_processor import EnhancedQueryProcessor
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSOR_AVAILABLE = False
    logging.warning("Enhanced query processor not available. Falling back to basic processing.")

# AI/LLM imports
try:
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain_anthropic import ChatAnthropic
    from langchain.prompts import ChatPromptTemplate
    from langchain.schema import HumanMessage, AIMessage
    from langchain.output_parsers import StrOutputParser
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("Lang<PERSON>hain not available. AI features will be limited.")

from utils.config import config

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """Types of query intents"""
    FIND = "find"
    COUNT = "count"
    AGGREGATE = "aggregate"
    DISTINCT = "distinct"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    SCHEMA = "schema"
    VISUALIZATION = "visualization"

@dataclass
class QueryAnalysis:
    """Analysis of a natural language query"""
    original_query: str
    intent: QueryIntent
    collections: List[str]
    fields: List[str]
    conditions: Dict[str, Any]
    sort_fields: List[Tuple[str, int]]  # (field, direction: 1 for asc, -1 for desc)
    limit: Optional[int]
    group_by: List[str]
    confidence: float
    reasoning: str
    needs_visualization: bool = False

class IntelligentQueryEngine:
    """
    Enhanced intelligent query engine that converts natural language to MongoDB queries
    using RAG-based semantic understanding and intelligent discovery
    """

    def __init__(self, mongo_client=None):
        """Initialize the query engine"""
        self.ai_available = LANGCHAIN_AVAILABLE and (
            config.ai.openai_api_key or config.ai.anthropic_api_key
        )

        if self.ai_available:
            self._initialize_ai_models()

        # Initialize intelligent query processor
        self.intelligent_processor = None
        if mongo_client:
            try:
                from .intelligent_query_processor import IntelligentQueryProcessor
                self.intelligent_processor = IntelligentQueryProcessor(mongo_client)
                logger.info("Intelligent query processor initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize intelligent query processor: {e}")
                self.intelligent_processor = None

        # Initialize enhanced query processor if available (fallback)
        self.enhanced_processor = None
        if ENHANCED_PROCESSOR_AVAILABLE:
            try:
                mongodb_uri = config.database.mongodb_uri
                self.enhanced_processor = EnhancedQueryProcessor(mongodb_uri)
                logger.info("Enhanced query processor initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize enhanced query processor: {e}")
                self.enhanced_processor = None

        # Pattern matching for common query types (fallback)
        self.intent_patterns = {
            QueryIntent.FIND: [
                r'\b(show|find|get|list|display|fetch)\b',
                r'\bwhat\s+(are|is)\b',
                r'\bgive\s+me\b'
            ],
            QueryIntent.COUNT: [
                r'\b(count|how\s+many|number\s+of)\b',
                r'\btotal\b'
            ],
            QueryIntent.AGGREGATE: [
                r'\b(sum|average|avg|max|min|group\s+by)\b',
                r'\btop\s+\d+\b',
                r'\bbottom\s+\d+\b'
            ],
            QueryIntent.VISUALIZATION: [
                r'\b(chart|graph|plot|visualize|visualization)\b',
                r'\b(bar\s+chart|pie\s+chart|line\s+chart)\b',
                r'\bshow\s+.*\s+(chart|graph)\b'
            ]
        }

        # Common field mappings (fallback)
        self.field_synonyms = {
            'price': ['cost', 'amount', 'value', 'price'],
            'name': ['title', 'label', 'name'],
            'date': ['created_at', 'updated_at', 'timestamp', 'date'],
            'id': ['_id', 'id', 'identifier'],
            'status': ['state', 'condition', 'status'],
            'category': ['type', 'kind', 'category', 'class']
        }
        
        logger.info(f"Query Engine initialized (AI available: {self.ai_available})")
    
    def _initialize_ai_models(self):
        """Initialize AI models for query generation"""
        try:
            if config.ai.anthropic_api_key:
                self.llm = ChatAnthropic(
                    model="claude-3-sonnet-20240229",
                    anthropic_api_key=config.ai.anthropic_api_key,
                    temperature=config.ai.temperature,
                    max_tokens=config.ai.max_tokens
                )
                logger.info("Initialized Claude AI model")
            elif config.ai.openai_api_key:
                self.llm = ChatOpenAI(
                    model=config.ai.default_model,
                    openai_api_key=config.ai.openai_api_key,
                    temperature=config.ai.temperature,
                    max_tokens=config.ai.max_tokens
                )
                logger.info("Initialized OpenAI model")
            else:
                self.ai_available = False
                logger.warning("No AI API keys available")
        except Exception as e:
            logger.error(f"Failed to initialize AI models: {str(e)}")
            self.ai_available = False
    
    async def analyze_query(self, query: str, schema_info: str = "") -> QueryAnalysis:
        """
        Analyze a natural language query and extract intent, entities, and structure
        
        Args:
            query: Natural language query
            schema_info: Database schema information for context
            
        Returns:
            QueryAnalysis object with extracted information
        """
        try:
            logger.info(f"Analyzing query: '{query}'")

            # Try enhanced processor first
            if self.enhanced_processor:
                analysis = await self._enhanced_analyze_query(query)
                analysis.needs_visualization = self._needs_visualization(query)
                logger.info(f"Enhanced query analysis complete: Intent={analysis.intent.value}, Confidence={analysis.confidence}")
                return analysis

            # Fallback to basic pattern matching
            intent = self._detect_intent(query)
            needs_viz = self._needs_visualization(query)

            if self.ai_available:
                # Use AI for advanced analysis
                analysis = await self._ai_analyze_query(query, schema_info)
                analysis.intent = intent  # Override with pattern-based intent if more confident
                analysis.needs_visualization = needs_viz
            else:
                # Fallback to pattern-based analysis
                analysis = self._pattern_analyze_query(query)
                analysis.intent = intent
                analysis.needs_visualization = needs_viz

            logger.info(f"Query analysis complete: Intent={analysis.intent.value}, Confidence={analysis.confidence}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing query: {str(e)}")
            # Return basic analysis as fallback
            return QueryAnalysis(
                original_query=query,
                intent=QueryIntent.FIND,
                collections=[],
                fields=[],
                conditions={},
                sort_fields=[],
                limit=None,
                group_by=[],
                confidence=0.3,
                reasoning=f"Error in analysis: {str(e)}",
                needs_visualization=False
            )

    async def _enhanced_analyze_query(self, query: str) -> QueryAnalysis:
        """Use enhanced query processor for semantic analysis"""
        try:
            logger.info("Using enhanced query processor for analysis")

            # Process query with enhanced processor
            result = self.enhanced_processor.process_query(query)

            # Convert intent string to QueryIntent enum
            intent_mapping = {
                'find_specific': QueryIntent.FIND,
                'count': QueryIntent.COUNT,
                'list_all': QueryIntent.FIND,
                'find_maximum': QueryIntent.AGGREGATE,
                'find_minimum': QueryIntent.AGGREGATE,
                'aggregate': QueryIntent.AGGREGATE,
                'general_search': QueryIntent.FIND
            }

            # Extract query context from enhanced processor
            query_context = self.enhanced_processor.discovery_system.query_intelligent_discovery(query)

            intent = intent_mapping.get(query_context.intent, QueryIntent.FIND)

            # Build collections list
            collections = []
            if result.collection_name and result.database_name:
                collections = [f"{result.database_name}.{result.collection_name}"]

            # Extract conditions from MongoDB query
            conditions = result.mongodb_query if result.mongodb_query else {}

            # Build sort fields based on intent
            sort_fields = []
            if intent == QueryIntent.AGGREGATE and query_context.intent in ['find_maximum', 'find_minimum']:
                # Find numeric fields for sorting
                for field in result.suggested_fields:
                    if any(keyword in field.lower() for keyword in ['price', 'cost', 'amount', 'value']):
                        direction = -1 if query_context.intent == 'find_maximum' else 1
                        sort_fields.append((field, direction))
                        break

            return QueryAnalysis(
                original_query=query,
                intent=intent,
                collections=collections,
                fields=result.suggested_fields,
                conditions=conditions,
                sort_fields=sort_fields,
                limit=100 if intent == QueryIntent.FIND else None,
                group_by=[],
                confidence=result.confidence_score,
                reasoning=result.explanation,
                needs_visualization=False  # Will be set by caller
            )

        except Exception as e:
            logger.error(f"Enhanced query analysis failed: {e}")
            raise e

    def _detect_intent(self, query: str) -> QueryIntent:
        """Detect query intent using pattern matching"""
        query_lower = query.lower()
        
        # Check each intent pattern
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        # Default to FIND
        return QueryIntent.FIND
    
    def _needs_visualization(self, query: str) -> bool:
        """Determine if query needs visualization"""
        viz_keywords = [
            'chart', 'graph', 'plot', 'visualize', 'visualization', 'visual',
            'bar chart', 'pie chart', 'line chart', 'histogram', 'scatter',
            'show', 'display', 'breakdown', 'distribution', 'trend'
        ]
        
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in viz_keywords)
    
    async def _ai_analyze_query(self, query: str, schema_info: str) -> QueryAnalysis:
        """Use AI to analyze the query"""
        try:
            prompt_template = """
            You are an expert MongoDB query analyzer. Analyze the following natural language query and extract structured information.

            Database Schema:
            {schema}

            User Query: "{query}"

            Please provide a JSON response with the following structure:
            {{
                "intent": "find|count|aggregate|distinct|insert|update|delete|schema",
                "collections": ["collection1", "collection2"],
                "fields": ["field1", "field2"],
                "conditions": {{"field": "value"}},
                "sort_fields": [["field", 1]],
                "limit": 10,
                "group_by": ["field"],
                "confidence": 0.8,
                "reasoning": "Brief explanation of analysis"
            }}

            Focus on:
            1. Identifying the main intent (what the user wants to do)
            2. Finding relevant collections from the schema
            3. Extracting field names and conditions
            4. Determining sorting and limiting requirements
            5. Identifying grouping needs for aggregations

            Be specific about field names from the actual schema provided.
            """
            
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()
            
            response = await chain.ainvoke({
                "query": query,
                "schema": schema_info
            })
            
            # Parse AI response
            try:
                ai_analysis = json.loads(response)
                return QueryAnalysis(
                    original_query=query,
                    intent=QueryIntent(ai_analysis.get('intent', 'find')),
                    collections=ai_analysis.get('collections', []),
                    fields=ai_analysis.get('fields', []),
                    conditions=ai_analysis.get('conditions', {}),
                    sort_fields=[(field, direction) for field, direction in ai_analysis.get('sort_fields', [])],
                    limit=ai_analysis.get('limit'),
                    group_by=ai_analysis.get('group_by', []),
                    confidence=ai_analysis.get('confidence', 0.7),
                    reasoning=ai_analysis.get('reasoning', 'AI analysis completed')
                )
            except json.JSONDecodeError:
                logger.warning("Failed to parse AI response as JSON, using pattern analysis")
                return self._pattern_analyze_query(query)
                
        except Exception as e:
            logger.error(f"AI analysis failed: {str(e)}")
            return self._pattern_analyze_query(query)
    
    def _pattern_analyze_query(self, query: str) -> QueryAnalysis:
        """Fallback pattern-based query analysis"""
        query_lower = query.lower()
        
        # Extract basic information using patterns
        collections = self._extract_collections(query_lower)
        fields = self._extract_fields(query_lower)
        conditions = self._extract_conditions(query_lower)
        limit = self._extract_limit(query_lower)
        sort_fields = self._extract_sort_fields(query_lower)
        
        return QueryAnalysis(
            original_query=query,
            intent=self._detect_intent(query),
            collections=collections,
            fields=fields,
            conditions=conditions,
            sort_fields=sort_fields,
            limit=limit,
            group_by=[],
            confidence=0.6,
            reasoning="Pattern-based analysis"
        )
    
    def _extract_collections(self, query: str) -> List[str]:
        """Extract potential collection names from query"""
        # Common collection names
        common_collections = [
            'users', 'products', 'orders', 'customers', 'items',
            'sales', 'inventory', 'reviews', 'categories', 'accounts'
        ]
        
        found_collections = []
        for collection in common_collections:
            if collection in query or collection[:-1] in query:  # singular form
                found_collections.append(collection)
        
        return found_collections
    
    def _extract_fields(self, query: str) -> List[str]:
        """Extract potential field names from query"""
        # Look for common field patterns
        fields = []
        
        # Check for field synonyms
        for canonical_field, synonyms in self.field_synonyms.items():
            for synonym in synonyms:
                if synonym in query:
                    fields.append(canonical_field)
                    break
        
        return fields
    
    def _extract_conditions(self, query: str) -> Dict[str, Any]:
        """Extract query conditions"""
        conditions = {}
        
        # Look for comparison patterns
        # "price > 100", "status = active", etc.
        comparison_patterns = [
            r'(\w+)\s*>\s*(\d+)',
            r'(\w+)\s*<\s*(\d+)',
            r'(\w+)\s*=\s*(\w+)',
            r'(\w+)\s+is\s+(\w+)'
        ]
        
        for pattern in comparison_patterns:
            matches = re.findall(pattern, query)
            for field, value in matches:
                # Try to convert to appropriate type
                try:
                    conditions[field] = int(value)
                except ValueError:
                    conditions[field] = value
        
        return conditions
    
    def _extract_limit(self, query: str) -> Optional[int]:
        """Extract limit from query"""
        # Look for "top 10", "first 5", "limit 20", etc.
        limit_patterns = [
            r'top\s+(\d+)',
            r'first\s+(\d+)',
            r'limit\s+(\d+)',
            r'(\d+)\s+results'
        ]
        
        for pattern in limit_patterns:
            match = re.search(pattern, query)
            if match:
                return int(match.group(1))
        
        return None
    
    def _extract_sort_fields(self, query: str) -> List[Tuple[str, int]]:
        """Extract sorting requirements"""
        sort_fields = []
        
        # Look for "sort by", "order by", etc.
        if 'sort' in query or 'order' in query:
            # Look for field names after sort/order keywords
            sort_pattern = r'(?:sort|order)\s+by\s+(\w+)(?:\s+(asc|desc|ascending|descending))?'
            match = re.search(sort_pattern, query)
            if match:
                field = match.group(1)
                direction = 1  # ascending by default
                if match.group(2) and match.group(2).lower().startswith('desc'):
                    direction = -1
                sort_fields.append((field, direction))
        
        return sort_fields

    async def generate_mongodb_query(self, analysis: QueryAnalysis, schema_info: str = "") -> str:
        """
        Generate MongoDB query from analysis

        Args:
            analysis: Query analysis result
            schema_info: Database schema information

        Returns:
            MongoDB query string in PyMongo format
        """
        try:
            if self.ai_available:
                # Use AI for query generation
                return await self._ai_generate_query(analysis, schema_info)
            else:
                # Use pattern-based generation
                return self._pattern_generate_query(analysis)

        except Exception as e:
            logger.error(f"Error generating MongoDB query: {str(e)}")
            # Return a safe fallback query
            return "list(db.collection.find().limit(10))"

    async def _ai_generate_query(self, analysis: QueryAnalysis, schema_info: str) -> str:
        """Use AI to generate MongoDB query"""
        try:
            prompt_template = """
            You are an expert MongoDB query generator. Based on the query analysis and database schema,
            generate a single-line PyMongo query that answers the user's question.

            Database Schema:
            {schema}

            Query Analysis:
            - Original Query: {original_query}
            - Intent: {intent}
            - Collections: {collections}
            - Fields: {fields}
            - Conditions: {conditions}
            - Sort Fields: {sort_fields}
            - Limit: {limit}
            - Group By: {group_by}

            CRITICAL RULES:
            1. Generate ONLY single-line PyMongo code
            2. Use 'db' for database operations
            3. NO imports, NO multi-line code, NO explanations
            4. Available functions: list, json, len, str, int, float, dict, set, tuple, range
            5. For visualization requests, return aggregated data suitable for charts
            6. Use proper MongoDB methods: find(), aggregate(), count_documents(), etc.
            7. Handle case-sensitive method names correctly

            EXAMPLES:
            Intent: find, Collections: [products] → list(db.products.find())
            Intent: count, Collections: [users] → db.users.count_documents({{}})
            Intent: aggregate, Group by category → list(db.products.aggregate([{{"$group": {{"_id": "$category", "count": {{"$sum": 1}}}}}}]))

            Generate the PyMongo query:
            """

            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()

            query = await chain.ainvoke({
                "schema": schema_info,
                "original_query": analysis.original_query,
                "intent": analysis.intent.value,
                "collections": analysis.collections,
                "fields": analysis.fields,
                "conditions": analysis.conditions,
                "sort_fields": analysis.sort_fields,
                "limit": analysis.limit,
                "group_by": analysis.group_by
            })

            # Clean up the response
            query = query.strip()
            if query.startswith('```'):
                # Remove code block markers
                lines = query.split('\n')
                query = '\n'.join(line for line in lines if not line.startswith('```'))
                query = query.strip()

            return query

        except Exception as e:
            logger.error(f"AI query generation failed: {str(e)}")
            return self._pattern_generate_query(analysis)

    def _pattern_generate_query(self, analysis: QueryAnalysis) -> str:
        """Generate MongoDB query using patterns"""
        try:
            # Determine collection (use first one or default)
            collection = analysis.collections[0] if analysis.collections else "collection"

            # Build query based on intent
            if analysis.intent == QueryIntent.FIND:
                return self._build_find_query(collection, analysis)
            elif analysis.intent == QueryIntent.COUNT:
                return self._build_count_query(collection, analysis)
            elif analysis.intent == QueryIntent.AGGREGATE:
                return self._build_aggregate_query(collection, analysis)
            else:
                # Default to find
                return self._build_find_query(collection, analysis)

        except Exception as e:
            logger.error(f"Pattern query generation failed: {str(e)}")
            return f"list(db.{collection}.find().limit(10))"

    def _build_find_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build a find query"""
        query_parts = [f"db.{collection}.find("]

        # Add conditions
        if analysis.conditions:
            conditions_str = json.dumps(analysis.conditions)
            query_parts.append(conditions_str)
        else:
            query_parts.append("{}")

        query_parts.append(")")

        # Add sorting
        if analysis.sort_fields:
            sort_dict = {field: direction for field, direction in analysis.sort_fields}
            sort_str = json.dumps(sort_dict)
            query_parts.append(f".sort({sort_str})")

        # Add limit
        limit = analysis.limit or 100  # Default limit
        query_parts.append(f".limit({limit})")

        # Wrap in list() to get results
        query = "list(" + "".join(query_parts) + ")"
        return query

    def _build_count_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build a count query"""
        if analysis.conditions:
            conditions_str = json.dumps(analysis.conditions)
            return f"db.{collection}.count_documents({conditions_str})"
        else:
            return f"db.{collection}.count_documents({{}})"

    def _build_aggregate_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build an aggregation query"""
        pipeline = []

        # Add match stage if conditions exist
        if analysis.conditions:
            pipeline.append({"$match": analysis.conditions})

        # Add group stage if group_by fields exist
        if analysis.group_by:
            group_stage = {
                "$group": {
                    "_id": f"${analysis.group_by[0]}",
                    "count": {"$sum": 1}
                }
            }
            pipeline.append(group_stage)

        # Add sort stage
        if analysis.sort_fields:
            sort_dict = {field: direction for field, direction in analysis.sort_fields}
            pipeline.append({"$sort": sort_dict})

        # Add limit
        if analysis.limit:
            pipeline.append({"$limit": analysis.limit})

        pipeline_str = json.dumps(pipeline)
        return f"list(db.{collection}.aggregate({pipeline_str}))"

    def get_query_suggestions(self, partial_query: str, schema_info: str = "") -> List[str]:
        """Get query suggestions based on partial input"""
        suggestions = [
            "Show me all products",
            "Count total orders",
            "Top 10 customers by sales",
            "Products by category",
            "Average order value",
            "Users registered this month",
            "Most expensive products",
            "Orders by status",
            "Create a chart of sales by month",
            "Find products without prices"
        ]

        # Filter suggestions based on partial query
        if partial_query:
            partial_lower = partial_query.lower()
            filtered = [s for s in suggestions if partial_lower in s.lower()]
            return filtered[:5]

        return suggestions[:5]

    async def process_query_intelligent(self, query: str, selected_database: str) -> Dict[str, Any]:
        """
        Process query with advanced intelligence and natural responses

        Args:
            query: Natural language query
            selected_database: Selected database name

        Returns:
            Dictionary with natural response and results
        """
        try:
            logger.info(f"Processing intelligent query: {query}")

            # Use enhanced processor if available
            if self.enhanced_processor:
                result = self.enhanced_processor.process_query(query, selected_database)

                # Generate natural response based on intent and results
                natural_response = self._generate_natural_response(query, result)

                return {
                    'success': result.get('success', True),
                    'user_query': query,
                    'response': natural_response,
                    'results': result.get('results', []),
                    'result_count': result.get('result_count', 0),
                    'execution_time': result.get('execution_time', 0),
                    'confidence_score': result.get('confidence_score', 0.0),
                    'collection_name': result.get('collection_name', ''),
                    'database_name': result.get('database_name', ''),
                    'error': result.get('error', None)
                }
            else:
                return {
                    'success': False,
                    'user_query': query,
                    'response': "I'm sorry, but the intelligent query system is not available right now. Please try again later.",
                    'results': [],
                    'result_count': 0,
                    'execution_time': 0,
                    'confidence_score': 0.0,
                    'error': 'Enhanced processor not available'
                }

        except Exception as e:
            logger.error(f"Intelligent query processing failed: {e}")
            return {
                'success': False,
                'user_query': query,
                'response': f"I encountered an error while processing your query: {str(e)}",
                'results': [],
                'result_count': 0,
                'execution_time': 0,
                'confidence_score': 0.0,
                'error': str(e)
            }

    def _generate_natural_response(self, query: str, result: Dict[str, Any]) -> str:
        """Generate natural language response based on query and results"""
        try:
            if not result.get('success', True):
                return result.get('response', 'I could not process your query.')

            results = result.get('results', [])
            result_count = result.get('result_count', 0)
            collection_name = result.get('collection_name', 'data')

            # Analyze query intent for response generation
            query_lower = query.lower()

            # Count queries
            if any(word in query_lower for word in ['how many', 'count', 'total', 'number of']):
                if result_count == 0:
                    return f"I found no {self._extract_entity_from_query(query)} matching your criteria."
                elif result_count == 1:
                    return f"I found 1 {self._extract_entity_from_query(query)} matching your criteria."
                else:
                    return f"I found {result_count} {self._extract_entity_from_query(query)} matching your criteria."

            # List/Show queries
            elif any(word in query_lower for word in ['list', 'show', 'display', 'get', 'find']):
                entity = self._extract_entity_from_query(query)

                if result_count == 0:
                    return f"I couldn't find any {entity} matching your criteria."

                response_parts = []
                if result_count == 1:
                    response_parts.append(f"I found 1 {entity}:")
                else:
                    response_parts.append(f"I found {result_count} {entity}:")

                # Format results naturally
                formatted_results = self._format_results_naturally(results, query)
                response_parts.extend(formatted_results)

                return "\n".join(response_parts)

            # Aggregation queries (max, min, average, etc.)
            elif any(word in query_lower for word in ['most expensive', 'cheapest', 'highest', 'lowest', 'average', 'maximum', 'minimum']):
                if result_count == 0:
                    return "I couldn't find any data to analyze for your query."

                return self._format_aggregation_response(query, results)

            # Default response for other queries
            else:
                if result_count == 0:
                    return "I couldn't find any data matching your query."

                entity = self._extract_entity_from_query(query)
                response_parts = [f"Here's what I found for your query about {entity}:"]
                formatted_results = self._format_results_naturally(results, query)
                response_parts.extend(formatted_results)

                return "\n".join(response_parts)

        except Exception as e:
            logger.error(f"Error generating natural response: {e}")
            return "I found some data but had trouble formatting the response. Please try rephrasing your query."

    def _extract_entity_from_query(self, query: str) -> str:
        """Extract the main entity/subject from the query"""
        query_lower = query.lower()

        # Common entities
        entities = {
            'product': ['product', 'item', 'goods'],
            'customer': ['customer', 'client', 'user'],
            'order': ['order', 'purchase', 'transaction'],
            'sale': ['sale', 'revenue', 'income'],
            'category': ['category', 'type', 'kind'],
            'review': ['review', 'rating', 'feedback'],
            'employee': ['employee', 'staff', 'worker'],
            'invoice': ['invoice', 'bill', 'receipt']
        }

        for entity, keywords in entities.items():
            for keyword in keywords:
                if keyword in query_lower:
                    return entity + 's' if not keyword.endswith('s') else entity

        # Fallback: try to extract noun after common verbs
        import re
        patterns = [
            r'(?:show|list|find|get|display)\s+(?:all\s+)?(?:the\s+)?(\w+)',
            r'how\s+many\s+(\w+)',
            r'count\s+(?:all\s+)?(\w+)',
            r'total\s+(?:number\s+of\s+)?(\w+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, query_lower)
            if match:
                entity = match.group(1)
                return entity + 's' if not entity.endswith('s') else entity

        return 'items'

    def _format_results_naturally(self, results: List[Dict], query: str) -> List[str]:
        """Format results in a natural, user-friendly way"""
        if not results:
            return []

        formatted = []
        query_lower = query.lower()

        # Limit results for readability
        display_results = results[:10]  # Show max 10 items

        for i, item in enumerate(display_results, 1):
            # Format each item based on its content
            item_parts = []

            # Handle common fields with natural formatting
            if 'name' in item:
                item_parts.append(f"**{item['name']}**")
            elif 'title' in item:
                item_parts.append(f"**{item['title']}**")
            elif 'product_name' in item:
                item_parts.append(f"**{item['product_name']}**")

            # Add price information
            price_fields = ['price', 'cost', 'amount', 'value']
            for field in price_fields:
                if field in item and item[field] is not None:
                    item_parts.append(f"${item[field]}")
                    break

            # Add category/type information
            category_fields = ['category', 'type', 'kind']
            for field in category_fields:
                if field in item and item[field]:
                    item_parts.append(f"({item[field]})")
                    break

            # Add other relevant fields based on query context
            if 'age' in query_lower and 'age' in item:
                item_parts.append(f"Age: {item['age']}")

            if 'email' in query_lower and 'email' in item:
                item_parts.append(f"Email: {item['email']}")

            if 'status' in query_lower and 'status' in item:
                item_parts.append(f"Status: {item['status']}")

            # Format the line
            if item_parts:
                formatted.append(f"{i}. {' - '.join(item_parts)}")
            else:
                # Fallback: show first few fields
                key_value_pairs = []
                for key, value in list(item.items())[:3]:
                    if key != '_id' and value is not None:
                        key_value_pairs.append(f"{key}: {value}")
                if key_value_pairs:
                    formatted.append(f"{i}. {' | '.join(key_value_pairs)}")

        # Add "and more" message if there are more results
        if len(results) > 10:
            formatted.append(f"\n...and {len(results) - 10} more items. Ask me to show more if you'd like to see them!")

        return formatted

    def _format_aggregation_response(self, query: str, results: List[Dict]) -> str:
        """Format aggregation results naturally"""
        if not results:
            return "No data found for your aggregation query."

        query_lower = query.lower()

        # Handle different aggregation types
        if 'most expensive' in query_lower or 'highest' in query_lower:
            item = results[0]
            name = item.get('name', item.get('title', item.get('product_name', 'Item')))
            price = item.get('price', item.get('cost', item.get('amount', 'N/A')))
            return f"The most expensive item is **{name}** at ${price}."

        elif 'cheapest' in query_lower or 'lowest' in query_lower:
            item = results[0]
            name = item.get('name', item.get('title', item.get('product_name', 'Item')))
            price = item.get('price', item.get('cost', item.get('amount', 'N/A')))
            return f"The cheapest item is **{name}** at ${price}."

        elif 'average' in query_lower:
            if len(results) == 1 and 'average' in str(results[0]).lower():
                avg_value = list(results[0].values())[0] if results[0] else 0
                return f"The average value is ${avg_value:.2f}."

        # Default aggregation formatting
        return f"Here are the aggregated results: {results[0] if results else 'No data'}"

    def get_collection_overview(self) -> Dict[str, Any]:
        """Get overview of available collections using enhanced processor"""
        try:
            if self.enhanced_processor:
                return self.enhanced_processor.get_collection_overview()
            else:
                return {
                    'total_collections': 0,
                    'databases': {},
                    'collections': [],
                    'error': 'Enhanced processor not available'
                }
        except Exception as e:
            logger.error(f"Failed to get collection overview: {e}")
            return {
                'total_collections': 0,
                'databases': {},
                'collections': [],
                'error': str(e)
            }

    def refresh_discovery_cache(self) -> bool:
        """Refresh the intelligent discovery cache"""
        try:
            if self.enhanced_processor:
                self.enhanced_processor.refresh_discovery_cache()
                return True
            else:
                logger.warning("Enhanced processor not available for cache refresh")
                return False
        except Exception as e:
            logger.error(f"Failed to refresh discovery cache: {e}")
            return False
