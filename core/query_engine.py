"""
Intelligent Query Engine for Natural Language to MongoDB Query Conversion
Uses AI to understand user intent and generate appropriate MongoDB queries
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import re
import json
from dataclasses import dataclass
from enum import Enum

# AI/LLM imports
try:
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain_anthropic import ChatAnthropic
    from langchain.prompts import ChatPromptTemplate
    from langchain.schema import HumanMessage, AIMessage
    from langchain.output_parsers import StrOutputParser
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("LangChain not available. AI features will be limited.")

from utils.config import config

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """Types of query intents"""
    FIND = "find"
    COUNT = "count"
    AGGREGATE = "aggregate"
    DISTINCT = "distinct"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    SCHEMA = "schema"
    VISUALIZATION = "visualization"

@dataclass
class QueryAnalysis:
    """Analysis of a natural language query"""
    original_query: str
    intent: QueryIntent
    collections: List[str]
    fields: List[str]
    conditions: Dict[str, Any]
    sort_fields: List[Tuple[str, int]]  # (field, direction: 1 for asc, -1 for desc)
    limit: Optional[int]
    group_by: List[str]
    confidence: float
    reasoning: str
    needs_visualization: bool = False

class IntelligentQueryEngine:
    """
    Intelligent query engine that converts natural language to MongoDB queries
    using AI and pattern matching techniques
    """
    
    def __init__(self):
        """Initialize the query engine"""
        self.ai_available = LANGCHAIN_AVAILABLE and (
            config.ai.openai_api_key or config.ai.anthropic_api_key
        )
        
        if self.ai_available:
            self._initialize_ai_models()
        
        # Pattern matching for common query types
        self.intent_patterns = {
            QueryIntent.FIND: [
                r'\b(show|find|get|list|display|fetch)\b',
                r'\bwhat\s+(are|is)\b',
                r'\bgive\s+me\b'
            ],
            QueryIntent.COUNT: [
                r'\b(count|how\s+many|number\s+of)\b',
                r'\btotal\b'
            ],
            QueryIntent.AGGREGATE: [
                r'\b(sum|average|avg|max|min|group\s+by)\b',
                r'\btop\s+\d+\b',
                r'\bbottom\s+\d+\b'
            ],
            QueryIntent.VISUALIZATION: [
                r'\b(chart|graph|plot|visualize|visualization)\b',
                r'\b(bar\s+chart|pie\s+chart|line\s+chart)\b',
                r'\bshow\s+.*\s+(chart|graph)\b'
            ]
        }
        
        # Common field mappings
        self.field_synonyms = {
            'price': ['cost', 'amount', 'value', 'price'],
            'name': ['title', 'label', 'name'],
            'date': ['created_at', 'updated_at', 'timestamp', 'date'],
            'id': ['_id', 'id', 'identifier'],
            'status': ['state', 'condition', 'status'],
            'category': ['type', 'kind', 'category', 'class']
        }
        
        logger.info(f"Query Engine initialized (AI available: {self.ai_available})")
    
    def _initialize_ai_models(self):
        """Initialize AI models for query generation"""
        try:
            if config.ai.anthropic_api_key:
                self.llm = ChatAnthropic(
                    model="claude-3-sonnet-20240229",
                    anthropic_api_key=config.ai.anthropic_api_key,
                    temperature=config.ai.temperature,
                    max_tokens=config.ai.max_tokens
                )
                logger.info("Initialized Claude AI model")
            elif config.ai.openai_api_key:
                self.llm = ChatOpenAI(
                    model=config.ai.default_model,
                    openai_api_key=config.ai.openai_api_key,
                    temperature=config.ai.temperature,
                    max_tokens=config.ai.max_tokens
                )
                logger.info("Initialized OpenAI model")
            else:
                self.ai_available = False
                logger.warning("No AI API keys available")
        except Exception as e:
            logger.error(f"Failed to initialize AI models: {str(e)}")
            self.ai_available = False
    
    async def analyze_query(self, query: str, schema_info: str = "") -> QueryAnalysis:
        """
        Analyze a natural language query and extract intent, entities, and structure
        
        Args:
            query: Natural language query
            schema_info: Database schema information for context
            
        Returns:
            QueryAnalysis object with extracted information
        """
        try:
            logger.info(f"Analyzing query: '{query}'")
            
            # Basic pattern matching
            intent = self._detect_intent(query)
            needs_viz = self._needs_visualization(query)
            
            if self.ai_available:
                # Use AI for advanced analysis
                analysis = await self._ai_analyze_query(query, schema_info)
                analysis.intent = intent  # Override with pattern-based intent if more confident
                analysis.needs_visualization = needs_viz
            else:
                # Fallback to pattern-based analysis
                analysis = self._pattern_analyze_query(query)
                analysis.intent = intent
                analysis.needs_visualization = needs_viz
            
            logger.info(f"Query analysis complete: Intent={analysis.intent.value}, Confidence={analysis.confidence}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing query: {str(e)}")
            # Return basic analysis as fallback
            return QueryAnalysis(
                original_query=query,
                intent=QueryIntent.FIND,
                collections=[],
                fields=[],
                conditions={},
                sort_fields=[],
                limit=None,
                group_by=[],
                confidence=0.3,
                reasoning=f"Error in analysis: {str(e)}",
                needs_visualization=False
            )
    
    def _detect_intent(self, query: str) -> QueryIntent:
        """Detect query intent using pattern matching"""
        query_lower = query.lower()
        
        # Check each intent pattern
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        # Default to FIND
        return QueryIntent.FIND
    
    def _needs_visualization(self, query: str) -> bool:
        """Determine if query needs visualization"""
        viz_keywords = [
            'chart', 'graph', 'plot', 'visualize', 'visualization', 'visual',
            'bar chart', 'pie chart', 'line chart', 'histogram', 'scatter',
            'show', 'display', 'breakdown', 'distribution', 'trend'
        ]
        
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in viz_keywords)
    
    async def _ai_analyze_query(self, query: str, schema_info: str) -> QueryAnalysis:
        """Use AI to analyze the query"""
        try:
            prompt_template = """
            You are an expert MongoDB query analyzer. Analyze the following natural language query and extract structured information.

            Database Schema:
            {schema}

            User Query: "{query}"

            Please provide a JSON response with the following structure:
            {{
                "intent": "find|count|aggregate|distinct|insert|update|delete|schema",
                "collections": ["collection1", "collection2"],
                "fields": ["field1", "field2"],
                "conditions": {{"field": "value"}},
                "sort_fields": [["field", 1]],
                "limit": 10,
                "group_by": ["field"],
                "confidence": 0.8,
                "reasoning": "Brief explanation of analysis"
            }}

            Focus on:
            1. Identifying the main intent (what the user wants to do)
            2. Finding relevant collections from the schema
            3. Extracting field names and conditions
            4. Determining sorting and limiting requirements
            5. Identifying grouping needs for aggregations

            Be specific about field names from the actual schema provided.
            """
            
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()
            
            response = await chain.ainvoke({
                "query": query,
                "schema": schema_info
            })
            
            # Parse AI response
            try:
                ai_analysis = json.loads(response)
                return QueryAnalysis(
                    original_query=query,
                    intent=QueryIntent(ai_analysis.get('intent', 'find')),
                    collections=ai_analysis.get('collections', []),
                    fields=ai_analysis.get('fields', []),
                    conditions=ai_analysis.get('conditions', {}),
                    sort_fields=[(field, direction) for field, direction in ai_analysis.get('sort_fields', [])],
                    limit=ai_analysis.get('limit'),
                    group_by=ai_analysis.get('group_by', []),
                    confidence=ai_analysis.get('confidence', 0.7),
                    reasoning=ai_analysis.get('reasoning', 'AI analysis completed')
                )
            except json.JSONDecodeError:
                logger.warning("Failed to parse AI response as JSON, using pattern analysis")
                return self._pattern_analyze_query(query)
                
        except Exception as e:
            logger.error(f"AI analysis failed: {str(e)}")
            return self._pattern_analyze_query(query)
    
    def _pattern_analyze_query(self, query: str) -> QueryAnalysis:
        """Fallback pattern-based query analysis"""
        query_lower = query.lower()
        
        # Extract basic information using patterns
        collections = self._extract_collections(query_lower)
        fields = self._extract_fields(query_lower)
        conditions = self._extract_conditions(query_lower)
        limit = self._extract_limit(query_lower)
        sort_fields = self._extract_sort_fields(query_lower)
        
        return QueryAnalysis(
            original_query=query,
            intent=self._detect_intent(query),
            collections=collections,
            fields=fields,
            conditions=conditions,
            sort_fields=sort_fields,
            limit=limit,
            group_by=[],
            confidence=0.6,
            reasoning="Pattern-based analysis"
        )
    
    def _extract_collections(self, query: str) -> List[str]:
        """Extract potential collection names from query"""
        # Common collection names
        common_collections = [
            'users', 'products', 'orders', 'customers', 'items',
            'sales', 'inventory', 'reviews', 'categories', 'accounts'
        ]
        
        found_collections = []
        for collection in common_collections:
            if collection in query or collection[:-1] in query:  # singular form
                found_collections.append(collection)
        
        return found_collections
    
    def _extract_fields(self, query: str) -> List[str]:
        """Extract potential field names from query"""
        # Look for common field patterns
        fields = []
        
        # Check for field synonyms
        for canonical_field, synonyms in self.field_synonyms.items():
            for synonym in synonyms:
                if synonym in query:
                    fields.append(canonical_field)
                    break
        
        return fields
    
    def _extract_conditions(self, query: str) -> Dict[str, Any]:
        """Extract query conditions"""
        conditions = {}
        
        # Look for comparison patterns
        # "price > 100", "status = active", etc.
        comparison_patterns = [
            r'(\w+)\s*>\s*(\d+)',
            r'(\w+)\s*<\s*(\d+)',
            r'(\w+)\s*=\s*(\w+)',
            r'(\w+)\s+is\s+(\w+)'
        ]
        
        for pattern in comparison_patterns:
            matches = re.findall(pattern, query)
            for field, value in matches:
                # Try to convert to appropriate type
                try:
                    conditions[field] = int(value)
                except ValueError:
                    conditions[field] = value
        
        return conditions
    
    def _extract_limit(self, query: str) -> Optional[int]:
        """Extract limit from query"""
        # Look for "top 10", "first 5", "limit 20", etc.
        limit_patterns = [
            r'top\s+(\d+)',
            r'first\s+(\d+)',
            r'limit\s+(\d+)',
            r'(\d+)\s+results'
        ]
        
        for pattern in limit_patterns:
            match = re.search(pattern, query)
            if match:
                return int(match.group(1))
        
        return None
    
    def _extract_sort_fields(self, query: str) -> List[Tuple[str, int]]:
        """Extract sorting requirements"""
        sort_fields = []
        
        # Look for "sort by", "order by", etc.
        if 'sort' in query or 'order' in query:
            # Look for field names after sort/order keywords
            sort_pattern = r'(?:sort|order)\s+by\s+(\w+)(?:\s+(asc|desc|ascending|descending))?'
            match = re.search(sort_pattern, query)
            if match:
                field = match.group(1)
                direction = 1  # ascending by default
                if match.group(2) and match.group(2).lower().startswith('desc'):
                    direction = -1
                sort_fields.append((field, direction))
        
        return sort_fields

    async def generate_mongodb_query(self, analysis: QueryAnalysis, schema_info: str = "") -> str:
        """
        Generate MongoDB query from analysis

        Args:
            analysis: Query analysis result
            schema_info: Database schema information

        Returns:
            MongoDB query string in PyMongo format
        """
        try:
            if self.ai_available:
                # Use AI for query generation
                return await self._ai_generate_query(analysis, schema_info)
            else:
                # Use pattern-based generation
                return self._pattern_generate_query(analysis)

        except Exception as e:
            logger.error(f"Error generating MongoDB query: {str(e)}")
            # Return a safe fallback query
            return "list(db.collection.find().limit(10))"

    async def _ai_generate_query(self, analysis: QueryAnalysis, schema_info: str) -> str:
        """Use AI to generate MongoDB query"""
        try:
            prompt_template = """
            You are an expert MongoDB query generator. Based on the query analysis and database schema,
            generate a single-line PyMongo query that answers the user's question.

            Database Schema:
            {schema}

            Query Analysis:
            - Original Query: {original_query}
            - Intent: {intent}
            - Collections: {collections}
            - Fields: {fields}
            - Conditions: {conditions}
            - Sort Fields: {sort_fields}
            - Limit: {limit}
            - Group By: {group_by}

            CRITICAL RULES:
            1. Generate ONLY single-line PyMongo code
            2. Use 'db' for database operations
            3. NO imports, NO multi-line code, NO explanations
            4. Available functions: list, json, len, str, int, float, dict, set, tuple, range
            5. For visualization requests, return aggregated data suitable for charts
            6. Use proper MongoDB methods: find(), aggregate(), count_documents(), etc.
            7. Handle case-sensitive method names correctly

            EXAMPLES:
            Intent: find, Collections: [products] → list(db.products.find())
            Intent: count, Collections: [users] → db.users.count_documents({{}})
            Intent: aggregate, Group by category → list(db.products.aggregate([{{"$group": {{"_id": "$category", "count": {{"$sum": 1}}}}}}]))

            Generate the PyMongo query:
            """

            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | self.llm | StrOutputParser()

            query = await chain.ainvoke({
                "schema": schema_info,
                "original_query": analysis.original_query,
                "intent": analysis.intent.value,
                "collections": analysis.collections,
                "fields": analysis.fields,
                "conditions": analysis.conditions,
                "sort_fields": analysis.sort_fields,
                "limit": analysis.limit,
                "group_by": analysis.group_by
            })

            # Clean up the response
            query = query.strip()
            if query.startswith('```'):
                # Remove code block markers
                lines = query.split('\n')
                query = '\n'.join(line for line in lines if not line.startswith('```'))
                query = query.strip()

            return query

        except Exception as e:
            logger.error(f"AI query generation failed: {str(e)}")
            return self._pattern_generate_query(analysis)

    def _pattern_generate_query(self, analysis: QueryAnalysis) -> str:
        """Generate MongoDB query using patterns"""
        try:
            # Determine collection (use first one or default)
            collection = analysis.collections[0] if analysis.collections else "collection"

            # Build query based on intent
            if analysis.intent == QueryIntent.FIND:
                return self._build_find_query(collection, analysis)
            elif analysis.intent == QueryIntent.COUNT:
                return self._build_count_query(collection, analysis)
            elif analysis.intent == QueryIntent.AGGREGATE:
                return self._build_aggregate_query(collection, analysis)
            else:
                # Default to find
                return self._build_find_query(collection, analysis)

        except Exception as e:
            logger.error(f"Pattern query generation failed: {str(e)}")
            return f"list(db.{collection}.find().limit(10))"

    def _build_find_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build a find query"""
        query_parts = [f"db.{collection}.find("]

        # Add conditions
        if analysis.conditions:
            conditions_str = json.dumps(analysis.conditions)
            query_parts.append(conditions_str)
        else:
            query_parts.append("{}")

        query_parts.append(")")

        # Add sorting
        if analysis.sort_fields:
            sort_dict = {field: direction for field, direction in analysis.sort_fields}
            sort_str = json.dumps(sort_dict)
            query_parts.append(f".sort({sort_str})")

        # Add limit
        limit = analysis.limit or 100  # Default limit
        query_parts.append(f".limit({limit})")

        # Wrap in list() to get results
        query = "list(" + "".join(query_parts) + ")"
        return query

    def _build_count_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build a count query"""
        if analysis.conditions:
            conditions_str = json.dumps(analysis.conditions)
            return f"db.{collection}.count_documents({conditions_str})"
        else:
            return f"db.{collection}.count_documents({{}})"

    def _build_aggregate_query(self, collection: str, analysis: QueryAnalysis) -> str:
        """Build an aggregation query"""
        pipeline = []

        # Add match stage if conditions exist
        if analysis.conditions:
            pipeline.append({"$match": analysis.conditions})

        # Add group stage if group_by fields exist
        if analysis.group_by:
            group_stage = {
                "$group": {
                    "_id": f"${analysis.group_by[0]}",
                    "count": {"$sum": 1}
                }
            }
            pipeline.append(group_stage)

        # Add sort stage
        if analysis.sort_fields:
            sort_dict = {field: direction for field, direction in analysis.sort_fields}
            pipeline.append({"$sort": sort_dict})

        # Add limit
        if analysis.limit:
            pipeline.append({"$limit": analysis.limit})

        pipeline_str = json.dumps(pipeline)
        return f"list(db.{collection}.aggregate({pipeline_str}))"

    def get_query_suggestions(self, partial_query: str, schema_info: str = "") -> List[str]:
        """Get query suggestions based on partial input"""
        suggestions = [
            "Show me all products",
            "Count total orders",
            "Top 10 customers by sales",
            "Products by category",
            "Average order value",
            "Users registered this month",
            "Most expensive products",
            "Orders by status",
            "Create a chart of sales by month",
            "Find products without prices"
        ]

        # Filter suggestions based on partial query
        if partial_query:
            partial_lower = partial_query.lower()
            filtered = [s for s in suggestions if partial_lower in s.lower()]
            return filtered[:5]

        return suggestions[:5]
