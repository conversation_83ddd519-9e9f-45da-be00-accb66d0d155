#!/usr/bin/env python3
"""
Quick test of the enhanced MongoDB query system
"""

import sys
from pymongo import MongoClient
from enhanced_data_profiler import EnhancedDataProfiler
from visualization_engine import VisualizationEngine

def test_connection(uri, database_name):
    """Test MongoDB connection and enhanced features"""
    print(f"🔗 Testing connection to {database_name}...")
    
    try:
        # Connect to MongoDB
        client = MongoClient(uri, serverSelectionTimeoutMS=10000)
        client.admin.command('ping')
        db = client[database_name]
        
        print("✅ MongoDB connection successful!")
        
        # List collections
        collections = db.list_collection_names()
        print(f"📚 Found {len(collections)} collections:")
        for col in collections:
            count = db[col].estimated_document_count()
            print(f"   - {col}: ~{count:,} documents")
        
        # Test enhanced data profiler
        print("\n🧠 Testing Enhanced Data Profiler...")
        profiler = EnhancedDataProfiler(client, database_name)
        
        # Profile just one collection for testing
        if collections:
            test_collection = collections[0]
            print(f"📊 Profiling collection: {test_collection}")
            
            collection = db[test_collection]
            sample_docs = list(collection.aggregate([
                {"$sample": {"size": min(50, collection.count_documents({}))}}
            ]))
            
            if sample_docs:
                # Test basic profiling
                print(f"✅ Successfully sampled {len(sample_docs)} documents")

                # Show sample document structure
                if sample_docs:
                    sample_doc = sample_docs[0]
                    fields = list(sample_doc.keys())
                    print(f"   - Sample fields: {fields[:5]}{'...' if len(fields) > 5 else ''}")
        
        # Test visualization engine
        print("\n📊 Testing Visualization Engine...")
        viz_engine = VisualizationEngine()
        
        # Test with sample data
        sample_results = [
            {"category": "A", "value": 10},
            {"category": "B", "value": 20},
            {"category": "C", "value": 15}
        ]
        
        should_viz = viz_engine.should_visualize("show distribution", sample_results, "distribution")
        print(f"✅ Visualization detection: {should_viz}")
        
        if should_viz:
            viz_result = viz_engine.generate_visualization(
                "show distribution", sample_results, "test_collection"
            )
            if viz_result:
                print(f"✅ Generated visualization: {viz_result['type']}")
            else:
                print("❌ Failed to generate visualization")
        
        print("\n🎉 All tests passed!")
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    if len(sys.argv) != 3:
        print("Usage: python quick_test.py <mongodb_uri> <database_name>")
        print("\nExample:")
        print("python quick_test.py 'mongodb+srv://user:<EMAIL>/' 'comics-generator'")
        sys.exit(1)
    
    uri = sys.argv[1]
    database_name = sys.argv[2]
    
    print("🧪 Quick MongoDB Enhanced System Test")
    print("=" * 50)
    
    success = test_connection(uri, database_name)
    
    if success:
        print("\n✅ Your MongoDB connection is working!")
        print("🚀 You can now use the enhanced query system with visualizations!")
        print("\nTo run the full system:")
        print("streamlit run simple_prototype.py --server.port 8506")
    else:
        print("\n❌ Connection test failed. Please check your MongoDB URI and database name.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
