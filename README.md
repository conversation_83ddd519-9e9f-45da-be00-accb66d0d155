# 🚀 DataPilot - Intelligent MongoDB Query System

**An AI-powered system for natural language querying of MongoDB databases with intelligent collection discovery and automatic visualization.**

## 🌟 Overview

DataPilot is a sophisticated MongoDB intelligence system that allows users to query their databases using natural language, automatically discovers relevant data across multiple collections, and generates visualizations - all without requiring users to know the database structure.

### ✨ Key Features

- 🗣️ **Natural Language Queries**: Ask questions in plain English
- 🔍 **Intelligent Collection Discovery**: Automatically finds relevant data across databases
- 📊 **Automatic Visualization**: Creates charts and graphs based on query results
- 🧠 **AI-Powered Query Generation**: Converts natural language to MongoDB queries
- 🚀 **Multi-Database Support**: Works across multiple MongoDB databases
- 📱 **Modern Web Interface**: Built with Streamlit for easy interaction
- 🐳 **Docker Ready**: Easy deployment with Docker and Docker Compose

## 🏗️ Architecture

### Core Components

1. **MongoDB Manager** (`core/mongodb_manager.py`)
   - Handles database connections and operations
   - Manages connection pooling and performance optimization
   - Provides schema discovery and caching

2. **Intelligent Query Engine** (`core/query_engine.py`)
   - Converts natural language to MongoDB queries
   - Uses AI (OpenAI/Anthropic) for advanced query understanding
   - Provides fallback pattern-based query generation

3. **Visualization Engine** (`core/visualization_engine.py`)
   - Automatically detects appropriate chart types
   - Creates interactive visualizations using Plotly
   - Supports bar charts, line charts, pie charts, scatter plots, and more

4. **Schema Discovery Engine** (`core/schema_discovery.py`)
   - Intelligently analyzes database schemas
   - Identifies semantic field types and relationships
   - Provides business context understanding

5. **UI Components** (`ui/components.py`)
   - Reusable Streamlit components
   - Modern, responsive interface elements
   - Comprehensive user experience features

## 🚀 Quick Start

### Option 1: Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd datapilot-backend
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your MongoDB URI and API keys
   ```

3. **Run with Docker Compose**:
   ```bash
   docker-compose up -d
   ```

4. **Access the application**:
   Open http://localhost:8501 in your browser

### Option 2: Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   # Create .env file with your settings
   MONGODB_URI=mongodb://localhost:27017
   OPENAI_API_KEY=your_openai_key_here
   ANTHROPIC_API_KEY=your_anthropic_key_here
   ```

3. **Run the application**:
   ```bash
   streamlit run app.py
   ```

## 📋 Requirements

### System Requirements
- Python 3.11+
- MongoDB 4.4+
- 2GB RAM minimum
- Modern web browser

### Dependencies
- **Core**: Streamlit, PyMongo, Pandas, NumPy
- **AI/ML**: LangChain, OpenAI, Anthropic
- **Visualization**: Plotly, Matplotlib, Seaborn
- **Optional**: Redis (for caching), Docker

## 🔧 Configuration

### Environment Variables

```bash
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
MONGODB_DATABASE=your_database_name

# AI Provider Keys (at least one required for full functionality)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
MAX_QUERY_RESULTS=1000
ENABLE_AUTO_VISUALIZATION=true

# Security (for production)
SECRET_KEY=your_secret_key_here
ENABLE_AUTHENTICATION=false

# Caching (optional)
REDIS_URL=redis://localhost:6379
CACHE_ENABLED=true
```

## 💡 Usage Examples

### Basic Queries
```
"Show me all products"
"Count total orders"
"Find users registered this month"
"What are the top 10 customers by sales?"
```

### Visualization Queries
```
"Create a pie chart of product categories"
"Show a bar chart of sales by month"
"Plot customer distribution by city"
"Visualize order trends over time"
```

### Advanced Queries
```
"Find products without prices"
"Show customers who haven't ordered in 30 days"
"Compare sales performance across regions"
"Analyze user engagement patterns"
```

## 🎯 Example Workflow

The system demonstrates the concept described in the original README:

### Step 1: User Query
User asks: *"What is the most expensive product?"*

### Step 2: Intelligent Analysis
- Analyzes query intent and entities
- Discovers relevant collections across databases
- Ranks collections by relevance score

### Step 3: Query Generation
- Generates optimized MongoDB queries for each collection
- Handles different field names (`price`, `cost`, `retail_price`)
- Applies appropriate filters and sorting

### Step 4: Parallel Execution
- Executes queries across multiple collections simultaneously
- Aggregates and normalizes results
- Handles different data schemas

### Step 5: Response Generation
- Creates natural language response
- Provides context about data sources
- Suggests follow-up queries

### Step 6: Automatic Visualization
- Detects visualization opportunities
- Creates appropriate charts (bar, pie, line, etc.)
- Provides interactive visualizations

## 🏢 Production Deployment

### Docker Deployment
```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose scale datapilot=3

# Monitor logs
docker-compose logs -f datapilot
```

### Environment Setup
- Use environment-specific `.env` files
- Configure proper MongoDB connection strings
- Set up monitoring and logging
- Enable authentication for production use

## 🔒 Security Considerations

- **Connection Security**: Use encrypted MongoDB connections (TLS/SSL)
- **API Key Management**: Store API keys securely using environment variables
- **Authentication**: Enable user authentication for production deployments
- **Query Sanitization**: Built-in protection against injection attacks
- **Access Control**: Implement role-based access control as needed

## 📊 Monitoring and Analytics

The system includes built-in monitoring:
- Query execution statistics
- Performance metrics
- Error tracking and logging
- Usage analytics
- Visualization creation metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review example queries
- Open an issue on GitHub
- Contact the development team

---

**DataPilot v1.0** - Making MongoDB accessible through natural language! 🚀