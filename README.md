# datapilot-backend


# MongoChat Example: Finding Products Across Collections

This example demonstrates how MongoChat can query across multiple collections and databases to find the most expensive product, even when the user doesn't specify which collection to search.

## Sample MongoDB Cluster Structure

Our example cluster has the following structure:

### Database: `retail_store`
- Collection: `products`
  - Contains basic product information
  - Fields: `name`, `category`, `price`, `in_stock`

### Database: `electronics_shop`
- Collection: `inventory`
  - Contains electronic items
  - Fields: `item_name`, `brand`, `cost`, `quantity`

### Database: `luxury_boutique`
- Collection: `merchandise`
  - Contains luxury items
  - Fields: `product_title`, `designer`, `retail_price`, `available`

## Example Query

Imagine a user types this query:

> "What is the most expensive product?"

## Step 1: Find Relevant Collections

MongoChat's cluster discovery service analyzes all collections and ranks them by relevance:

1. `luxury_boutique.merchandise` (Score: 8.2)
   - Has price-related fields: `retail_price`
   - Has product-related fields: `product_title`
   - Collection name "merchandise" matches product concept

2. `retail_store.products` (Score: 7.5)
   - Has price-related fields: `price`
   - Has product-related fields: `name`
   - Direct match with "products" collection name

3. `electronics_shop.inventory` (Score: 4.1)
   - Has price-related fields: `cost`
   - Has product-related fields: `item_name`
   - Lower relevance due to "inventory" not directly matching "product"

## Step 2: Generate Collection-Specific Queries

For each relevant collection, MongoChat generates a tailored query:

### For `luxury_boutique.merchandise`:
```javascript
{
  "operation": "find",
  "query": { "available": true },
  "sort": { "retail_price": -1 },
  "limit": 10
}
```

### For `retail_store.products`:
```javascript
{
  "operation": "find",
  "query": { "in_stock": true },
  "sort": { "price": -1 },
  "limit": 10
}
```

### For `electronics_shop.inventory`:
```javascript
{
  "operation": "find",
  "query": { "quantity": { "$gt": 0 } },
  "sort": { "cost": -1 },
  "limit": 10
}
```

## Step 3: Execute Queries in Parallel

MongoChat executes these queries across the three collections simultaneously.

## Step 4: Aggregate Results

Results from all collections are combined and normalized:

| Name | Price | Database | Collection |
|------|-------|----------|------------|
| Diamond Encrusted Watch | $24,999.99 | luxury_boutique | merchandise |
| Platinum Necklace | $12,500.00 | luxury_boutique | merchandise |
| Limited Edition Gaming PC | $4,299.99 | electronics_shop | inventory |
| Designer Handbag | $3,999.99 | luxury_boutique | merchandise |
| 85" OLED TV | $3,499.99 | retail_store | products |
| ... | ... | ... | ... |

## Step 5: Generate Response

MongoChat creates a natural language response:

> "The most expensive product I found is a Diamond Encrusted Watch priced at $24,999.99. This item is from the 'merchandise' collection in the 'luxury_boutique' database. I searched across 3 collections in 3 databases to find this result."

## Step 6: Create Visualization

MongoChat automatically generates a bar chart comparing the prices of the top products:

```
Price ($)
│
│   $24,999.99
│   ┌──────┐
│   │      │
│   │      │
│   │      │   $12,500.00
│   │      │   ┌──────┐
│   │      │   │      │               $4,299.99
│   │      │   │      │               ┌──────┐   $3,999.99   $3,499.99
│   │      │   │      │               │      │   ┌──────┐    ┌──────┐
└───┴──────┴───┴──────┴───────────────┴──────┴───┴──────┴────┴──────┴───
    Diamond    Platinum   Limited Ed.  Designer    85" OLED
    Watch      Necklace   Gaming PC    Handbag     TV
```

## Key Technical Aspects Demonstrated

1. **Cross-Database Discovery**: Found relevant collections across multiple databases
2. **Field Mapping**: Recognized different field names (`price`, `cost`, `retail_price`) as semantically equivalent
3. **Query Customization**: Generated appropriate filters for each collection (`in_stock`, `available`, `quantity`)
4. **Result Normalization**: Combined and ranked results despite different schemas
5. **Visualization**: Created an appropriate chart for the price comparison

This example shows how MongoChat can work with real-world MongoDB deployments where data is spread across multiple collections with varying schemas, all without requiring the user to know the specific structure of the database.