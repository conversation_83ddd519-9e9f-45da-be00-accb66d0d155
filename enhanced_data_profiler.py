"""
Enhanced Data Profiler for MongoDB
Creates deep understanding of data structure, relationships, and patterns
"""

import pymongo
from pymongo import MongoClient
from collections import defaultdict, Counter
import re
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime, timedelta
import statistics

class EnhancedDataProfiler:
    """Deep data understanding system for MongoDB"""
    
    def __init__(self, client: MongoClient, database_name: str):
        self.client = client
        self.db = client[database_name]
        self.profile = {}
        
    def build_comprehensive_profile(self, sample_size: int = 1000) -> Dict[str, Any]:
        """Build comprehensive data profile"""
        print("🔍 Building comprehensive data profile...")
        
        collections = self.db.list_collection_names()
        
        for collection_name in collections:
            print(f"  📊 Profiling {collection_name}...")
            collection = self.db[collection_name]
            
            # Get sample documents
            total_docs = collection.count_documents({})
            sample_docs = list(collection.aggregate([
                {"$sample": {"size": min(sample_size, total_docs)}}
            ]))
            
            if not sample_docs:
                continue
                
            self.profile[collection_name] = {
                'basic_info': self._get_basic_info(collection, total_docs),
                'field_analysis': self._analyze_fields(sample_docs),
                'relationships': self._detect_relationships(collection_name, sample_docs),
                'patterns': self._detect_patterns(sample_docs),
                'business_context': self._infer_business_context(collection_name, sample_docs)
            }
        
        # Cross-collection relationship analysis
        self._analyze_cross_collection_relationships()
        
        return self.profile
    
    def _get_basic_info(self, collection, total_docs: int) -> Dict[str, Any]:
        """Get basic collection information"""
        return {
            'total_documents': total_docs,
            'collection_name': collection.name,
            'indexes': list(collection.list_indexes()),
            'estimated_size': collection.estimated_document_count()
        }
    
    def _analyze_fields(self, sample_docs: List[Dict]) -> Dict[str, Any]:
        """Deep field analysis"""
        field_stats = defaultdict(lambda: {
            'type_distribution': Counter(),
            'sample_values': [],
            'null_count': 0,
            'unique_values': set(),
            'patterns': []
        })
        
        for doc in sample_docs:
            self._analyze_document_fields(doc, field_stats)
        
        # Process field statistics
        processed_fields = {}
        for field_name, stats in field_stats.items():
            processed_fields[field_name] = self._process_field_stats(field_name, stats, len(sample_docs))
        
        return processed_fields
    
    def _analyze_document_fields(self, doc: Dict, field_stats: Dict, prefix: str = ""):
        """Recursively analyze document fields"""
        for key, value in doc.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if value is None:
                field_stats[full_key]['null_count'] += 1
            else:
                # Type analysis
                value_type = type(value).__name__
                field_stats[full_key]['type_distribution'][value_type] += 1
                
                # Sample values (limit to avoid memory issues)
                if len(field_stats[full_key]['sample_values']) < 20:
                    field_stats[full_key]['sample_values'].append(value)
                
                # Unique values (for low cardinality fields)
                if len(field_stats[full_key]['unique_values']) < 100:
                    field_stats[full_key]['unique_values'].add(str(value))
                
                # Pattern detection for strings
                if isinstance(value, str):
                    self._detect_string_patterns(value, field_stats[full_key]['patterns'])
                
                # Recursive analysis for nested objects
                if isinstance(value, dict):
                    self._analyze_document_fields(value, field_stats, full_key)
    
    def _process_field_stats(self, field_name: str, stats: Dict, total_docs: int) -> Dict[str, Any]:
        """Process and enrich field statistics"""
        primary_type = stats['type_distribution'].most_common(1)[0][0] if stats['type_distribution'] else 'unknown'
        
        result = {
            'primary_type': primary_type,
            'type_distribution': dict(stats['type_distribution']),
            'null_percentage': (stats['null_count'] / total_docs) * 100,
            'sample_values': stats['sample_values'][:10],  # Limit samples
            'cardinality': len(stats['unique_values']),
            'is_likely_id': self._is_likely_id_field(field_name, stats['sample_values']),
            'is_categorical': self._is_categorical_field(stats['unique_values'], total_docs),
            'patterns': list(set(stats['patterns']))
        }
        
        # Numeric analysis
        if primary_type in ['int', 'float']:
            numeric_values = [v for v in stats['sample_values'] if isinstance(v, (int, float))]
            if numeric_values:
                result.update({
                    'min_value': min(numeric_values),
                    'max_value': max(numeric_values),
                    'avg_value': statistics.mean(numeric_values),
                    'median_value': statistics.median(numeric_values)
                })
        
        # String analysis
        if primary_type == 'str':
            string_values = [v for v in stats['sample_values'] if isinstance(v, str)]
            if string_values:
                result.update({
                    'avg_length': statistics.mean([len(s) for s in string_values]),
                    'common_prefixes': self._find_common_prefixes(string_values),
                    'contains_email': any('@' in s for s in string_values),
                    'contains_url': any('http' in s for s in string_values)
                })
        
        return result
    
    def _detect_string_patterns(self, value: str, patterns: List[str]):
        """Detect common string patterns"""
        if re.match(r'^[A-Z][a-z]+ [A-Z][a-z]+$', value):
            patterns.append('FirstName LastName')
        elif re.match(r'^\w+@\w+\.\w+$', value):
            patterns.append('Email')
        elif re.match(r'^\d{4}-\d{2}-\d{2}', value):
            patterns.append('Date')
        elif re.match(r'^https?://', value):
            patterns.append('URL')
        elif re.match(r'^\d+$', value):
            patterns.append('Numeric String')
    
    def _is_likely_id_field(self, field_name: str, sample_values: List) -> bool:
        """Detect if field is likely an ID"""
        id_indicators = ['id', '_id', 'key', 'uuid', 'guid']
        name_lower = field_name.lower()
        
        # Name-based detection
        if any(indicator in name_lower for indicator in id_indicators):
            return True
        
        # Pattern-based detection
        if sample_values:
            # Check if values look like IDs (long strings, UUIDs, etc.)
            sample_str = str(sample_values[0]) if sample_values else ""
            if len(sample_str) > 20 or re.match(r'^[a-f0-9-]{36}$', sample_str):
                return True
        
        return False
    
    def _is_categorical_field(self, unique_values: set, total_docs: int) -> bool:
        """Detect if field is categorical"""
        if not unique_values:
            return False
        
        # If unique values are less than 10% of total docs, likely categorical
        uniqueness_ratio = len(unique_values) / total_docs
        return uniqueness_ratio < 0.1 and len(unique_values) < 50
    
    def _find_common_prefixes(self, strings: List[str]) -> List[str]:
        """Find common prefixes in strings"""
        if len(strings) < 2:
            return []
        
        prefixes = Counter()
        for s in strings:
            for i in range(1, min(len(s), 10)):  # Check prefixes up to 10 chars
                prefix = s[:i]
                prefixes[prefix] += 1
        
        # Return prefixes that appear in at least 30% of strings
        threshold = len(strings) * 0.3
        return [prefix for prefix, count in prefixes.items() if count >= threshold]
    
    def _detect_relationships(self, collection_name: str, sample_docs: List[Dict]) -> Dict[str, Any]:
        """Detect potential relationships with other collections"""
        relationships = {}
        
        # Look for foreign key patterns
        for doc in sample_docs[:10]:  # Check first 10 docs
            for field_name, value in doc.items():
                if self._is_likely_foreign_key(field_name, value):
                    target_collection = self._guess_target_collection(field_name)
                    if target_collection and target_collection != collection_name:
                        relationships[field_name] = {
                            'target_collection': target_collection,
                            'relationship_type': 'foreign_key',
                            'confidence': 0.8
                        }
        
        return relationships
    
    def _is_likely_foreign_key(self, field_name: str, value: Any) -> bool:
        """Check if field looks like a foreign key"""
        field_lower = field_name.lower()
        
        # Common foreign key patterns
        fk_patterns = ['_id', 'id', 'ref', 'key']
        
        return any(pattern in field_lower for pattern in fk_patterns) and field_lower != '_id'
    
    def _guess_target_collection(self, field_name: str) -> Optional[str]:
        """Guess target collection from field name"""
        field_lower = field_name.lower()
        
        # Remove common suffixes
        for suffix in ['_id', 'id', '_ref', 'ref', '_key', 'key']:
            if field_lower.endswith(suffix):
                base_name = field_lower[:-len(suffix)]
                # Try plural form
                if base_name.endswith('s'):
                    return base_name
                else:
                    return base_name + 's'
        
        return None
    
    def _detect_patterns(self, sample_docs: List[Dict]) -> Dict[str, Any]:
        """Detect data patterns and anomalies"""
        patterns = {
            'common_structures': [],
            'nested_levels': 0,
            'array_fields': [],
            'temporal_fields': []
        }
        
        # Analyze document structures
        structures = Counter()
        max_depth = 0
        
        for doc in sample_docs:
            structure = self._get_document_structure(doc)
            structures[structure] += 1
            
            depth = self._get_document_depth(doc)
            max_depth = max(max_depth, depth)
            
            # Find array and temporal fields
            self._find_special_fields(doc, patterns)
        
        patterns['common_structures'] = [struct for struct, count in structures.most_common(3)]
        patterns['nested_levels'] = max_depth
        
        return patterns
    
    def _get_document_structure(self, doc: Dict, prefix: str = "") -> str:
        """Get document structure signature"""
        keys = []
        for key, value in doc.items():
            full_key = f"{prefix}.{key}" if prefix else key
            if isinstance(value, dict):
                keys.append(f"{full_key}:object")
            elif isinstance(value, list):
                keys.append(f"{full_key}:array")
            else:
                keys.append(f"{full_key}:{type(value).__name__}")
        
        return "|".join(sorted(keys))
    
    def _get_document_depth(self, obj: Any, current_depth: int = 0) -> int:
        """Calculate maximum nesting depth"""
        if not isinstance(obj, dict):
            return current_depth
        
        max_depth = current_depth
        for value in obj.values():
            if isinstance(value, dict):
                depth = self._get_document_depth(value, current_depth + 1)
                max_depth = max(max_depth, depth)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                depth = self._get_document_depth(value[0], current_depth + 1)
                max_depth = max(max_depth, depth)
        
        return max_depth
    
    def _find_special_fields(self, doc: Dict, patterns: Dict, prefix: str = ""):
        """Find array and temporal fields"""
        for key, value in doc.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, list):
                patterns['array_fields'].append(full_key)
            elif isinstance(value, (datetime, str)):
                # Check if string looks like a date
                if isinstance(value, str) and re.match(r'\d{4}-\d{2}-\d{2}', str(value)):
                    patterns['temporal_fields'].append(full_key)
            elif isinstance(value, dict):
                self._find_special_fields(value, patterns, full_key)
    
    def _infer_business_context(self, collection_name: str, sample_docs: List[Dict]) -> Dict[str, Any]:
        """Infer business context and meaning"""
        context = {
            'domain': self._guess_domain(collection_name, sample_docs),
            'entity_type': self._guess_entity_type(collection_name),
            'key_metrics': self._identify_key_metrics(sample_docs),
            'business_rules': self._infer_business_rules(collection_name, sample_docs)
        }
        
        return context
    
    def _guess_domain(self, collection_name: str, sample_docs: List[Dict]) -> str:
        """Guess business domain"""
        name_lower = collection_name.lower()
        
        # Domain keywords
        domains = {
            'ecommerce': ['product', 'order', 'customer', 'cart', 'payment'],
            'content': ['post', 'article', 'comment', 'user', 'media'],
            'comics': ['character', 'comic', 'hero', 'villain', 'story'],
            'analytics': ['event', 'metric', 'stat', 'log', 'track']
        }
        
        for domain, keywords in domains.items():
            if any(keyword in name_lower for keyword in keywords):
                return domain
        
        return 'general'
    
    def _guess_entity_type(self, collection_name: str) -> str:
        """Guess what type of entity this collection represents"""
        name_lower = collection_name.lower()
        
        if any(word in name_lower for word in ['user', 'customer', 'person', 'people']):
            return 'person'
        elif any(word in name_lower for word in ['product', 'item', 'goods']):
            return 'product'
        elif any(word in name_lower for word in ['order', 'transaction', 'purchase']):
            return 'transaction'
        elif any(word in name_lower for word in ['event', 'log', 'activity']):
            return 'event'
        else:
            return 'entity'
    
    def _identify_key_metrics(self, sample_docs: List[Dict]) -> List[str]:
        """Identify potential key metrics/KPIs"""
        metrics = []
        
        # Look for numeric fields that could be metrics
        for doc in sample_docs[:5]:
            for key, value in doc.items():
                if isinstance(value, (int, float)) and key.lower() in [
                    'price', 'amount', 'total', 'count', 'quantity', 'score', 
                    'rating', 'revenue', 'cost', 'profit', 'age', 'weight'
                ]:
                    metrics.append(key)
        
        return list(set(metrics))
    
    def _infer_business_rules(self, collection_name: str, sample_docs: List[Dict]) -> Dict[str, str]:
        """Infer common business rules"""
        rules = {}
        
        # Example business rules based on collection type
        name_lower = collection_name.lower()
        
        if 'customer' in name_lower:
            rules['active_customer'] = 'has orders in last 90 days'
            rules['vip_customer'] = 'total order value > $1000'
        elif 'product' in name_lower:
            rules['popular_product'] = 'high order frequency'
            rules['expensive_product'] = 'price > average price'
        elif 'order' in name_lower:
            rules['large_order'] = 'amount > $500'
            rules['recent_order'] = 'created in last 30 days'
        
        return rules
    
    def _analyze_cross_collection_relationships(self):
        """Analyze relationships between collections"""
        # This would analyze foreign key relationships across collections
        # and build a relationship graph
        pass
    
    def save_profile(self, filename: str):
        """Save profile to file"""
        with open(filename, 'w') as f:
            json.dump(self.profile, f, indent=2, default=str)
    
    def get_query_suggestions(self, collection_name: str) -> List[str]:
        """Generate intelligent query suggestions based on profile"""
        if collection_name not in self.profile:
            return []
        
        profile = self.profile[collection_name]
        suggestions = []
        
        # Basic queries
        suggestions.append(f"How many {collection_name} are there?")
        suggestions.append(f"Show me some {collection_name}")
        
        # Field-based queries
        for field_name, field_info in profile['field_analysis'].items():
            if field_info['is_categorical']:
                suggestions.append(f"Group {collection_name} by {field_name}")
            
            if field_info['primary_type'] in ['int', 'float']:
                suggestions.append(f"What's the average {field_name}?")
                suggestions.append(f"Find {collection_name} with highest {field_name}")
        
        # Business context queries
        business_context = profile['business_context']
        for rule_name, rule_desc in business_context['business_rules'].items():
            suggestions.append(f"Show me {rule_name} {collection_name}")
        
        return suggestions[:10]  # Limit to 10 suggestions
