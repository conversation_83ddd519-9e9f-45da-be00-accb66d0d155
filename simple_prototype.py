"""
Enhanced MongoDB Natural Language Query System - Prototype
MongoDB-first approach with deep data understanding and intelligent query planning
"""

import streamlit as st
import pymongo
from pymongo import MongoClient
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import time
from enhanced_data_profiler import EnhancedDataProfiler
from visualization_engine import VisualizationEngine

# Configure page
st.set_page_config(
    page_title="Simple MongoDB Query System",
    page_icon="🚀",
    layout="wide"
)

class EnhancedQueryEngine:
    """Enhanced MongoDB query engine with deep data understanding"""

    def __init__(self):
        self.client = None
        self.db = None
        self.schema_cache = {}
        self.data_profile = {}
        self.profiler = None
        self.viz_engine = VisualizationEngine()
        
        # Enhanced intent patterns
        self.patterns = {
            'count': [r'\b(how many|count|total|number of)\b'],
            'find_specific': [r'\b(what is|what\'s|tell me about|show me about)\b.*\b(of|about)\b'],
            'find_attribute': [r'\b(what is the|what\'s the|get the|find the)\b.*\b(of|for)\b'],
            'distribution': [r'\b(distribution|breakdown|group by|show.*by)\b'],
            'comparison': [r'\b(compare|comparison|top|bottom|rank)\b'],
            'trend': [r'\b(trend|over time|timeline|monthly|daily)\b'],
            'find': [r'\b(show|list|get|find|display|give me)\b'],
            'max': [r'\b(highest|maximum|largest|biggest|most|max)\b'],
            'min': [r'\b(lowest|minimum|smallest|least|min)\b'],
            'avg': [r'\b(average|avg|mean)\b'],
            'sum': [r'\b(sum|total of|add up)\b']
        }
        
        # Operator patterns
        self.operators = {
            'greater than': '$gt',
            'greater': '$gt',
            '>': '$gt',
            'less than': '$lt',
            'less': '$lt',
            '<': '$lt',
            'equal': '$eq',
            'equals': '$eq',
            '=': '$eq',
            'not equal': '$ne',
            '!=': '$ne',
            'contains': '$regex',
            'like': '$regex'
        }
    
    def connect(self, uri: str, database: str) -> bool:
        """Connect to MongoDB and build enhanced data profile"""
        try:
            self.client = MongoClient(uri, serverSelectionTimeoutMS=5000)
            self.client.admin.command('ping')
            self.db = self.client[database]

            # Build enhanced data profile
            with st.spinner("🧠 Building intelligent data profile..."):
                self.profiler = EnhancedDataProfiler(self.client, database)
                self.data_profile = self.profiler.build_comprehensive_profile()
                self._build_schema_cache()

            st.success("✅ Enhanced data profile built successfully!")
            return True
        except Exception as e:
            st.error(f"Connection failed: {e}")
            return False
    
    def _build_schema_cache(self):
        """Build lightweight schema cache"""
        self.schema_cache = {}
        try:
            collections = self.db.list_collection_names()
            for collection_name in collections:
                collection = self.db[collection_name]
                # Sample a few documents to understand schema
                sample_docs = list(collection.find().limit(5))
                if sample_docs:
                    fields = set()
                    for doc in sample_docs:
                        fields.update(doc.keys())
                    self.schema_cache[collection_name] = {
                        'fields': list(fields),
                        'count': collection.count_documents({}),
                        'sample': sample_docs[0] if sample_docs else {}
                    }
        except Exception as e:
            st.error(f"Schema discovery failed: {e}")
    
    def detect_intent(self, query: str) -> str:
        """Detect query intent using simple patterns"""
        query_lower = query.lower()
        
        for intent, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        return 'find'  # Default intent
    
    def find_collection(self, query: str) -> Optional[str]:
        """Find the most relevant collection using enhanced data understanding"""
        query_lower = query.lower()

        # Direct collection name match
        for collection_name in self.data_profile.keys():
            if collection_name.lower() in query_lower:
                return collection_name

        # Enhanced semantic matching using data profile
        best_match = None
        best_score = 0

        for collection_name, profile in self.data_profile.items():
            score = 0

            # Business context matching
            business_context = profile.get('business_context', {})
            domain = business_context.get('domain', '')
            entity_type = business_context.get('entity_type', '')

            # Domain-based scoring
            if domain in query_lower:
                score += 3
            if entity_type in query_lower:
                score += 2

            # Field analysis matching
            field_analysis = profile.get('field_analysis', {})
            for field_name, field_info in field_analysis.items():
                if field_name.lower() in query_lower:
                    score += 2

                # Check sample values for semantic matching
                sample_values = field_info.get('sample_values', [])
                for sample in sample_values:
                    if isinstance(sample, str) and sample.lower() in query_lower:
                        score += 1

            # Relationship-based matching
            relationships = profile.get('relationships', {})
            for rel_field, rel_info in relationships.items():
                target_collection = rel_info.get('target_collection', '')
                if target_collection.lower() in query_lower:
                    score += 1

            if score > best_score:
                best_score = score
                best_match = collection_name

        return best_match
    
    def parse_query_components(self, query: str, collection_name: str) -> Dict[str, Any]:
        """Parse query into components: target_field, filter_field, filter_value"""
        query_lower = query.lower()

        if collection_name not in self.data_profile:
            return {'target_field': None, 'filter_field': None, 'filter_value': None}

        profile = self.data_profile[collection_name]
        field_analysis = profile.get('field_analysis', {})

        # Parse different query patterns
        result = {
            'target_field': None,  # What field to return/show
            'filter_field': None,  # What field to filter by
            'filter_value': None,  # What value to filter for
            'operator': '$eq'
        }

        # Pattern 1: "What is the [TARGET_FIELD] of [FILTER_VALUE]?"
        # Example: "What is the age of John Smith?"
        match = re.search(r'what is the (\w+) of (.+?)(?:\s+customer|\s+user|\s*\?|$)', query_lower)
        if match:
            target_field_name = match.group(1)
            filter_value_text = match.group(2).strip()

            # Find target field
            result['target_field'] = self._find_field_by_name(target_field_name, field_analysis)

            # Find filter field and value
            filter_info = self._find_filter_field_and_value(filter_value_text, field_analysis)
            result['filter_field'] = filter_info['field']
            result['filter_value'] = filter_info['value']

            return result

        # Pattern 2: "Tell me about [FILTER_VALUE]"
        # Example: "Tell me about John Smith"
        match = re.search(r'tell me about (.+?)(?:\s+customer|\s+user|\s*\?|$)', query_lower)
        if match:
            filter_value_text = match.group(1).strip()

            # Find filter field and value
            filter_info = self._find_filter_field_and_value(filter_value_text, field_analysis)
            result['filter_field'] = filter_info['field']
            result['filter_value'] = filter_info['value']

            return result

        # Pattern 3: "Show [ENTITY] with [FIELD] [OPERATOR] [VALUE]"
        # Example: "Show customers with age greater than 25"
        for op_text, op_symbol in self.operators.items():
            if op_text in query_lower:
                result['operator'] = op_symbol
                parts = query_lower.split(op_text)
                if len(parts) >= 2:
                    # Extract field from left part
                    left_part = parts[0].strip()
                    field_match = re.search(r'(\w+)\s*$', left_part)
                    if field_match:
                        field_name = field_match.group(1)
                        result['filter_field'] = self._find_field_by_name(field_name, field_analysis)

                    # Extract value from right part
                    right_part = parts[1].strip()
                    result['filter_value'] = self._extract_value(right_part, result['filter_field'], field_analysis)

                    return result

        # Fallback: Try to find any recognizable field and value
        fallback_info = self._find_filter_field_and_value(query_lower, field_analysis)
        result['filter_field'] = fallback_info['field']
        result['filter_value'] = fallback_info['value']

        return result

    def _find_field_by_name(self, field_name: str, field_analysis: Dict) -> Optional[str]:
        """Find field by name with fuzzy matching"""
        field_name_lower = field_name.lower()

        # Direct match
        for field in field_analysis.keys():
            if field.lower() == field_name_lower:
                return field

        # Partial match
        for field in field_analysis.keys():
            if field_name_lower in field.lower() or field.lower() in field_name_lower:
                return field

        return None

    def _find_filter_field_and_value(self, text: str, field_analysis: Dict) -> Dict[str, Any]:
        """Find the best field and value to filter by"""
        best_field = None
        best_value = None
        best_confidence = 0

        for field_name, field_info in field_analysis.items():
            confidence = 0
            found_value = None

            # Check if any sample values appear in the text
            sample_values = field_info.get('sample_values', [])
            for sample in sample_values:
                if isinstance(sample, str) and len(sample) > 2:
                    if sample.lower() in text.lower():
                        confidence += 3
                        found_value = sample
                        break

            # Check for name patterns (common case)
            if field_name.lower() in ['name', 'customer_name', 'user_name', 'full_name']:
                # Look for name-like patterns in text
                name_match = re.search(r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b', text)
                if name_match:
                    confidence += 2
                    found_value = name_match.group(1)

            if confidence > best_confidence:
                best_confidence = confidence
                best_field = field_name
                best_value = found_value

        return {'field': best_field, 'value': best_value}

    def _extract_value(self, value_text: str, field_name: Optional[str], field_analysis: Dict) -> Any:
        """Extract and convert value based on field type"""
        if not field_name or field_name not in field_analysis:
            # Try to parse as number first, then string
            try:
                if '.' in value_text:
                    return float(re.findall(r'\d+\.?\d*', value_text)[0])
                else:
                    return int(re.findall(r'\d+', value_text)[0])
            except:
                return value_text.strip()

        field_info = field_analysis[field_name]
        primary_type = field_info.get('primary_type', 'str')

        if primary_type in ['int', 'float']:
            try:
                if '.' in value_text:
                    return float(re.findall(r'\d+\.?\d*', value_text)[0])
                else:
                    return int(re.findall(r'\d+', value_text)[0])
            except:
                return value_text.strip()
        else:
            return value_text.strip()
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """Process natural language query"""
        start_time = time.time()
        
        try:
            # Step 1: Detect intent
            intent = self.detect_intent(query)
            
            # Step 2: Find collection
            collection_name = self.find_collection(query)
            if not collection_name:
                return {
                    'success': False,
                    'message': f"❌ Couldn't find a relevant collection. Available: {', '.join(self.schema_cache.keys())}",
                    'suggestions': list(self.schema_cache.keys())
                }
            
            collection = self.db[collection_name]

            # Step 3: Parse query components
            query_components = self.parse_query_components(query, collection_name)

            # Step 4: Build and execute query based on intent
            if intent == 'count':
                result = self._handle_count_query(query, collection, collection_name, query_components)
            elif intent in ['max', 'min']:
                result = self._handle_minmax_query(query, collection, collection_name, intent, query_components)
            elif intent in ['find_specific', 'find_attribute']:
                result = self._handle_specific_query(query, collection, collection_name, query_components)
            elif intent == 'distribution':
                result = self._handle_distribution_query(query, collection, collection_name, query_components)
            elif intent == 'comparison':
                result = self._handle_comparison_query(query, collection, collection_name, query_components)
            elif intent == 'trend':
                result = self._handle_trend_query(query, collection, collection_name, query_components)
            else:  # find
                result = self._handle_find_query(query, collection, collection_name, query_components)

            # Step 5: Generate visualization if appropriate
            if result.get('success') and result.get('results'):
                viz_result = self.viz_engine.generate_visualization(
                    query, result['results'], collection_name, intent
                )
                if viz_result:
                    result['visualization'] = viz_result

            result['execution_time'] = time.time() - start_time
            result['collection'] = collection_name
            result['intent'] = intent
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f"❌ Query failed: {str(e)}",
                'execution_time': time.time() - start_time
            }
    
    def _handle_specific_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle specific information queries like 'What is the age of John Smith?'"""
        filter_field = components.get('filter_field')
        filter_value = components.get('filter_value')
        target_field = components.get('target_field')
        operator = components.get('operator', '$eq')

        if not filter_field or filter_value is None:
            return {
                'success': False,
                'message': f"❌ Couldn't understand what to search for in the query: '{query}'"
            }

        # Build filter
        if operator == '$eq':
            query_filter = {filter_field: filter_value}
        else:
            query_filter = {filter_field: {operator: filter_value}}

        # Build projection if target field specified
        projection = {}
        if target_field:
            projection = {target_field: 1, filter_field: 1, '_id': 0}

        # Execute query
        if projection:
            cursor = collection.find(query_filter, projection)
        else:
            cursor = collection.find(query_filter)

        results = list(cursor.limit(10))

        if not results:
            return {
                'success': False,
                'message': f"❌ No documents found with {filter_field} = '{filter_value}'"
            }

        # Format response based on query type
        if target_field and len(results) == 1:
            # Specific field query
            doc = results[0]
            if target_field in doc:
                value = doc[target_field]
                return {
                    'success': True,
                    'message': f"✅ {target_field} of {filter_value}: **{value}**",
                    'results': results,
                    'mongodb_query': f"db.{collection.name}.findOne({json.dumps(query_filter)}, {json.dumps(projection)})"
                }
            else:
                return {
                    'success': False,
                    'message': f"❌ Field '{target_field}' not found for {filter_value}"
                }
        else:
            # General information query
            return {
                'success': True,
                'message': f"✅ Found **{len(results)}** document(s) for {filter_value}",
                'results': results,
                'mongodb_query': f"db.{collection.name}.find({json.dumps(query_filter)}" +
                               (f", {json.dumps(projection)})" if projection else ")")
            }

    def _handle_count_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle count queries"""
        filter_field = components.get('filter_field')
        filter_value = components.get('filter_value')
        operator = components.get('operator', '$eq')

        if filter_field and filter_value is not None:
            # Count with condition
            if operator == '$eq':
                query_filter = {filter_field: filter_value}
            else:
                query_filter = {filter_field: {operator: filter_value}}
            count = collection.count_documents(query_filter)
            mongodb_query = f"db.{collection.name}.countDocuments({json.dumps(query_filter)})"
        else:
            # Count all
            count = collection.count_documents({})
            mongodb_query = f"db.{collection.name}.countDocuments({{}})"

        return {
            'success': True,
            'message': f"✅ Found **{count}** documents in {collection.name}",
            'count': count,
            'mongodb_query': mongodb_query
        }
    
    def _handle_find_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle find queries"""
        filter_field = components.get('filter_field')
        filter_value = components.get('filter_value')
        operator = components.get('operator', '$eq')

        if filter_field and filter_value is not None:
            # Find with condition
            if operator == '$eq':
                query_filter = {filter_field: filter_value}
            else:
                query_filter = {filter_field: {operator: filter_value}}
        else:
            # Find all (but limit to avoid overwhelming)
            query_filter = {}

        # Execute query
        cursor = collection.find(query_filter).limit(10)
        results = list(cursor)
        total_count = collection.count_documents(query_filter)

        mongodb_query = f"db.{collection.name}.find({json.dumps(query_filter)}).limit(10)"

        if filter_field and filter_value and total_count == 0:
            return {
                'success': False,
                'message': f"❌ No documents found with {filter_field} = '{filter_value}'",
                'mongodb_query': mongodb_query
            }

        return {
            'success': True,
            'message': f"✅ Found **{total_count}** documents in {collection.name}" +
                      (f" (showing first 10)" if total_count > 10 else ""),
            'results': results,
            'count': total_count,
            'mongodb_query': mongodb_query
        }
    
    def _handle_minmax_query(self, query: str, collection, collection_name: str, intent: str, components: Dict) -> Dict[str, Any]:
        """Handle min/max queries"""
        target_field = components.get('target_field')
        filter_field = components.get('filter_field')
        filter_value = components.get('filter_value')

        # Determine field to find min/max of
        field = target_field
        if not field:
            # Try to find numeric field using data profile
            if collection_name in self.data_profile:
                field_analysis = self.data_profile[collection_name].get('field_analysis', {})
                for field_name, field_info in field_analysis.items():
                    if field_info.get('primary_type') in ['int', 'float']:
                        field = field_name
                        break

        if not field:
            return {
                'success': False,
                'message': f"❌ Couldn't find a numeric field for {intent} operation"
            }

        # Build aggregation pipeline
        pipeline = []

        # Add filter stage if needed
        if filter_field and filter_value is not None:
            pipeline.append({'$match': {filter_field: filter_value}})

        # Add sort and limit
        sort_order = -1 if intent == 'max' else 1
        pipeline.extend([
            {'$sort': {field: sort_order}},
            {'$limit': 1}
        ])

        result = list(collection.aggregate(pipeline))
        mongodb_query = f"db.{collection.name}.aggregate({json.dumps(pipeline)})"

        if result:
            doc = result[0]
            value = doc.get(field, 'N/A')
            return {
                'success': True,
                'message': f"✅ {intent.title()} {field}: **{value}**",
                'results': result,
                'mongodb_query': mongodb_query
            }
        else:
            return {
                'success': False,
                'message': f"❌ No results found for {intent} {field}"
            }

    def _handle_distribution_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle distribution queries like 'Show distribution of ages'"""
        # Find field to group by
        group_field = self._find_grouping_field(query, collection_name)

        if not group_field:
            return {
                'success': False,
                'message': f"❌ Couldn't identify field to group by in query: '{query}'"
            }

        # Build aggregation pipeline
        pipeline = [
            {'$group': {'_id': f'${group_field}', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 20}
        ]

        results = list(collection.aggregate(pipeline))

        # Format results for better display
        formatted_results = []
        for doc in results:
            formatted_results.append({
                group_field: doc['_id'],
                'count': doc['count']
            })

        return {
            'success': True,
            'message': f"✅ Distribution of {group_field} (top 20)",
            'results': formatted_results,
            'mongodb_query': f"db.{collection.name}.aggregate({json.dumps(pipeline)})"
        }

    def _handle_comparison_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle comparison queries like 'Compare products by price'"""
        # Find fields for comparison
        group_field = self._find_grouping_field(query, collection_name)
        numeric_field = self._find_numeric_field(query, collection_name)

        if not group_field:
            return {
                'success': False,
                'message': f"❌ Couldn't identify field to compare by in query: '{query}'"
            }

        # Build aggregation pipeline
        if numeric_field:
            # Compare by numeric values (average)
            pipeline = [
                {'$group': {
                    '_id': f'${group_field}',
                    f'avg_{numeric_field}': {'$avg': f'${numeric_field}'},
                    'count': {'$sum': 1}
                }},
                {'$sort': {f'avg_{numeric_field}': -1}},
                {'$limit': 15}
            ]
        else:
            # Compare by count
            pipeline = [
                {'$group': {'_id': f'${group_field}', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}},
                {'$limit': 15}
            ]

        results = list(collection.aggregate(pipeline))

        # Format results
        formatted_results = []
        for doc in results:
            result_doc = {group_field: doc['_id']}
            if numeric_field:
                result_doc[f'avg_{numeric_field}'] = doc[f'avg_{numeric_field}']
            result_doc['count'] = doc['count']
            formatted_results.append(result_doc)

        return {
            'success': True,
            'message': f"✅ Comparison by {group_field}" + (f" (average {numeric_field})" if numeric_field else " (count)"),
            'results': formatted_results,
            'mongodb_query': f"db.{collection.name}.aggregate({json.dumps(pipeline)})"
        }

    def _handle_trend_query(self, query: str, collection, collection_name: str, components: Dict) -> Dict[str, Any]:
        """Handle trend queries like 'Show sales trend over time'"""
        # Find time field
        time_field = self._find_time_field(query, collection_name)
        numeric_field = self._find_numeric_field(query, collection_name)

        if not time_field:
            return {
                'success': False,
                'message': f"❌ Couldn't identify time field for trend analysis in query: '{query}'"
            }

        # Build aggregation pipeline for monthly trends
        if numeric_field:
            pipeline = [
                {'$group': {
                    '_id': {
                        'year': {'$year': f'${time_field}'},
                        'month': {'$month': f'${time_field}'}
                    },
                    f'avg_{numeric_field}': {'$avg': f'${numeric_field}'},
                    'count': {'$sum': 1}
                }},
                {'$sort': {'_id.year': 1, '_id.month': 1}},
                {'$limit': 24}  # Last 2 years
            ]
        else:
            pipeline = [
                {'$group': {
                    '_id': {
                        'year': {'$year': f'${time_field}'},
                        'month': {'$month': f'${time_field}'}
                    },
                    'count': {'$sum': 1}
                }},
                {'$sort': {'_id.year': 1, '_id.month': 1}},
                {'$limit': 24}
            ]

        results = list(collection.aggregate(pipeline))

        # Format results
        formatted_results = []
        for doc in results:
            result_doc = {
                'period': f"{doc['_id']['year']}-{doc['_id']['month']:02d}",
                'count': doc['count']
            }
            if numeric_field:
                result_doc[f'avg_{numeric_field}'] = doc[f'avg_{numeric_field}']
            formatted_results.append(result_doc)

        return {
            'success': True,
            'message': f"✅ Trend analysis over time" + (f" (average {numeric_field})" if numeric_field else " (count)"),
            'results': formatted_results,
            'mongodb_query': f"db.{collection.name}.aggregate({json.dumps(pipeline)})"
        }

    def _find_grouping_field(self, query: str, collection_name: str) -> Optional[str]:
        """Find field to group by based on query"""
        if collection_name not in self.data_profile:
            return None

        field_analysis = self.data_profile[collection_name].get('field_analysis', {})
        query_lower = query.lower()

        # Look for categorical fields mentioned in query
        for field_name, field_info in field_analysis.items():
            if field_name.lower() in query_lower and field_info.get('is_categorical', False):
                return field_name

        # Fallback to first categorical field
        for field_name, field_info in field_analysis.items():
            if field_info.get('is_categorical', False):
                return field_name

        return None

    def _find_numeric_field(self, query: str, collection_name: str) -> Optional[str]:
        """Find numeric field mentioned in query"""
        if collection_name not in self.data_profile:
            return None

        field_analysis = self.data_profile[collection_name].get('field_analysis', {})
        query_lower = query.lower()

        # Look for numeric fields mentioned in query
        for field_name, field_info in field_analysis.items():
            if field_name.lower() in query_lower and field_info.get('primary_type') in ['int', 'float']:
                return field_name

        return None

    def _find_time_field(self, query: str, collection_name: str) -> Optional[str]:
        """Find time/date field for trend analysis"""
        if collection_name not in self.data_profile:
            return None

        field_analysis = self.data_profile[collection_name].get('field_analysis', {})

        # Look for time fields
        for field_name, field_info in field_analysis.items():
            if any(word in field_name.lower() for word in ['date', 'time', 'created', 'updated']):
                return field_name

        return None

    def _display_data_results(self, results: List[Dict]):
        """Helper method to display query results"""
        for i, doc in enumerate(results[:10], 1):
            with st.expander(f"Document {i}", expanded=(i <= 3)):
                st.json(doc)

# Initialize session state
if 'query_engine' not in st.session_state:
    st.session_state.query_engine = EnhancedQueryEngine()
if 'connected' not in st.session_state:
    st.session_state.connected = False
if 'query_history' not in st.session_state:
    st.session_state.query_history = []

# Main UI
st.title("🧠 Enhanced MongoDB Query System")
st.markdown("**MongoDB-first approach with deep data understanding - Intelligent & Accurate**")

# Sidebar for connection
with st.sidebar:
    st.header("🔗 Connection")
    
    if not st.session_state.connected:
        uri = st.text_input("MongoDB URI", 
                           value="mongodb+srv://username:<EMAIL>/",
                           type="password")
        database = st.text_input("Database Name", value="")
        
        if st.button("Connect"):
            if uri and database:
                if st.session_state.query_engine.connect(uri, database):
                    st.session_state.connected = True
                    st.success("✅ Connected!")
                    st.rerun()
    else:
        st.success("✅ Connected to MongoDB")
        if st.button("Disconnect"):
            st.session_state.connected = False
            st.session_state.query_engine = EnhancedQueryEngine()
            st.rerun()
        
        # Show enhanced data profile
        if st.session_state.query_engine.data_profile:
            st.subheader("🧠 Intelligent Data Profile")
            for name, profile in st.session_state.query_engine.data_profile.items():
                basic_info = profile.get('basic_info', {})
                business_context = profile.get('business_context', {})

                # Collection summary
                doc_count = basic_info.get('total_documents', 0)
                domain = business_context.get('domain', 'general')
                entity_type = business_context.get('entity_type', 'entity')

                st.write(f"**{name}** ({doc_count} docs)")
                st.caption(f"Domain: {domain} | Type: {entity_type}")

                with st.expander(f"🔍 Deep Analysis: {name}"):
                    # Field analysis
                    field_analysis = profile.get('field_analysis', {})
                    st.write("**Key Fields:**")
                    for field_name, field_info in list(field_analysis.items())[:5]:
                        field_type = field_info.get('primary_type', 'unknown')
                        is_categorical = field_info.get('is_categorical', False)
                        cardinality = field_info.get('cardinality', 0)

                        category_info = " (categorical)" if is_categorical else f" ({cardinality} unique)"
                        st.write(f"  • {field_name}: {field_type}{category_info}")

                    # Business rules
                    business_rules = business_context.get('business_rules', {})
                    if business_rules:
                        st.write("**Business Rules:**")
                        for rule_name, rule_desc in business_rules.items():
                            st.write(f"  • {rule_name}: {rule_desc}")

                    # Relationships
                    relationships = profile.get('relationships', {})
                    if relationships:
                        st.write("**Relationships:**")
                        for field, rel_info in relationships.items():
                            target = rel_info.get('target_collection', 'unknown')
                            st.write(f"  • {field} → {target}")

            # Query suggestions
            st.subheader("💡 Smart Suggestions")
            if st.session_state.query_engine.profiler:
                selected_collection = st.selectbox(
                    "Get suggestions for:",
                    list(st.session_state.query_engine.data_profile.keys())
                )

                if selected_collection:
                    suggestions = st.session_state.query_engine.profiler.get_query_suggestions(selected_collection)
                    for suggestion in suggestions[:5]:
                        if st.button(f"💬 {suggestion}", key=f"suggest_{suggestion}"):
                            st.session_state.suggested_query = suggestion

# Main query interface
if st.session_state.connected:
    st.subheader("💬 Ask Your Database")
    
    # Query examples
    with st.expander("💡 Example Queries"):
        st.markdown("""
        **Count Queries:**
        - How many customers are there?
        - Count all products
        - Total number of orders

        **Find Queries:**
        - Show me all customers
        - List products with price greater than 100
        - Find customers with age less than 30

        **Analytics:**
        - What's the highest price?
        - Show me the oldest customer
        - Find the minimum order amount

        **📊 Visualization Queries:**
        - Show distribution of customer ages
        - Compare products by price
        - Show sales trends over time
        - What's the breakdown of product categories?
        - Show relationship between price and ratings
        """)
    
    # Query input with suggestion support
    default_query = st.session_state.get('suggested_query', '')
    query = st.text_input("Enter your question:",
                         value=default_query,
                         placeholder="How many customers do we have?")

    # Clear suggestion after use
    if 'suggested_query' in st.session_state:
        del st.session_state.suggested_query
    
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        if st.button("🚀 Execute", type="primary"):
            if query:
                with st.spinner("Processing..."):
                    result = st.session_state.query_engine.process_query(query)
                    st.session_state.query_history.append({
                        'query': query,
                        'result': result,
                        'timestamp': datetime.now()
                    })
    
    with col2:
        if st.button("🧹 Clear"):
            st.rerun()
    
    # Display results
    if st.session_state.query_history:
        latest = st.session_state.query_history[-1]
        result = latest['result']
        
        st.subheader("📋 Results")
        
        # Show main message
        if result['success']:
            st.success(result['message'])

            # Show visualization if available
            if result.get('visualization'):
                viz = result['visualization']
                st.subheader(f"📈 {viz['title']}")
                st.caption(viz['description'])
                st.plotly_chart(viz['figure'], use_container_width=True)

                # Add visualization insights
                if viz['type'] in ['comparison', 'distribution']:
                    st.info("💡 **Tip**: Click and drag to zoom, double-click to reset view")

            # Show data if available
            if 'results' in result and result['results']:
                # If we have visualization, make data section collapsible
                if result.get('visualization'):
                    with st.expander("📊 Raw Data", expanded=False):
                        self._display_data_results(result['results'])
                else:
                    st.subheader("📄 Data")
                    self._display_data_results(result['results'])

            # Show MongoDB query
            if 'mongodb_query' in result:
                with st.expander("🔍 MongoDB Query", expanded=False):
                    st.code(result['mongodb_query'], language='javascript')

            # Show performance
            if 'execution_time' in result:
                st.info(f"⚡ Executed in {result['execution_time']:.3f} seconds")
        else:
            st.error(result['message'])

            # Suggest visualization-friendly queries on error
            if st.session_state.query_engine.data_profile:
                collection_name = result.get('collection')
                if collection_name:
                    suggestions = st.session_state.query_engine.viz_engine.suggest_visualization_queries(
                        collection_name, st.session_state.query_engine.data_profile
                    )
                    if suggestions:
                        st.info("💡 **Try these visualization-friendly queries:**")
                        for suggestion in suggestions:
                            if st.button(f"📊 {suggestion}", key=f"viz_suggest_{suggestion}"):
                                st.session_state.suggested_query = suggestion
                                st.rerun()
            if 'suggestions' in result:
                st.info(f"💡 Available collections: {', '.join(result['suggestions'])}")

else:
    st.info("👈 Please connect to your MongoDB database first")
    
    # Show demo info
    st.subheader("🎨 Enhanced Intelligence + Visualization Features")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        **🚀 Deep Data Understanding:**
        - Comprehensive data profiling
        - Relationship detection
        - Business context inference
        - Semantic field matching
        - Value pattern recognition
        """)

    with col2:
        st.markdown("""
        **🎯 Intelligent Query Processing:**
        - Context-aware field detection
        - Smart value matching
        - Business rule integration
        - Cross-collection relationships
        - Predictable & accurate results
        """)

    with col3:
        st.markdown("""
        **📊 Smart Visualizations:**
        - Automatic chart detection
        - Interactive Plotly charts
        - Multiple chart types
        - Trend & distribution analysis
        - Visualization suggestions
        """)
