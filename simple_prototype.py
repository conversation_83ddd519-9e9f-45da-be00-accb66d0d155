"""
Enhanced MongoDB Natural Language Query System - Prototype
MongoDB-first approach with deep data understanding and intelligent query planning
"""

import streamlit as st
import pymongo
from pymongo import MongoClient
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import time
from enhanced_data_profiler import EnhancedDataProfiler

# Configure page
st.set_page_config(
    page_title="Simple MongoDB Query System",
    page_icon="🚀",
    layout="wide"
)

class EnhancedQueryEngine:
    """Enhanced MongoDB query engine with deep data understanding"""

    def __init__(self):
        self.client = None
        self.db = None
        self.schema_cache = {}
        self.data_profile = {}
        self.profiler = None
        
        # Simple intent patterns
        self.patterns = {
            'count': [r'\b(how many|count|total|number of)\b'],
            'find': [r'\b(show|list|get|find|display|give me)\b'],
            'max': [r'\b(highest|maximum|largest|biggest|most|max)\b'],
            'min': [r'\b(lowest|minimum|smallest|least|min)\b'],
            'avg': [r'\b(average|avg|mean)\b'],
            'sum': [r'\b(sum|total of|add up)\b']
        }
        
        # Operator patterns
        self.operators = {
            'greater than': '$gt',
            'greater': '$gt',
            '>': '$gt',
            'less than': '$lt',
            'less': '$lt',
            '<': '$lt',
            'equal': '$eq',
            'equals': '$eq',
            '=': '$eq',
            'not equal': '$ne',
            '!=': '$ne',
            'contains': '$regex',
            'like': '$regex'
        }
    
    def connect(self, uri: str, database: str) -> bool:
        """Connect to MongoDB and build enhanced data profile"""
        try:
            self.client = MongoClient(uri, serverSelectionTimeoutMS=5000)
            self.client.admin.command('ping')
            self.db = self.client[database]

            # Build enhanced data profile
            with st.spinner("🧠 Building intelligent data profile..."):
                self.profiler = EnhancedDataProfiler(self.client, database)
                self.data_profile = self.profiler.build_comprehensive_profile()
                self._build_schema_cache()

            st.success("✅ Enhanced data profile built successfully!")
            return True
        except Exception as e:
            st.error(f"Connection failed: {e}")
            return False
    
    def _build_schema_cache(self):
        """Build lightweight schema cache"""
        self.schema_cache = {}
        try:
            collections = self.db.list_collection_names()
            for collection_name in collections:
                collection = self.db[collection_name]
                # Sample a few documents to understand schema
                sample_docs = list(collection.find().limit(5))
                if sample_docs:
                    fields = set()
                    for doc in sample_docs:
                        fields.update(doc.keys())
                    self.schema_cache[collection_name] = {
                        'fields': list(fields),
                        'count': collection.count_documents({}),
                        'sample': sample_docs[0] if sample_docs else {}
                    }
        except Exception as e:
            st.error(f"Schema discovery failed: {e}")
    
    def detect_intent(self, query: str) -> str:
        """Detect query intent using simple patterns"""
        query_lower = query.lower()
        
        for intent, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        return 'find'  # Default intent
    
    def find_collection(self, query: str) -> Optional[str]:
        """Find the most relevant collection using enhanced data understanding"""
        query_lower = query.lower()

        # Direct collection name match
        for collection_name in self.data_profile.keys():
            if collection_name.lower() in query_lower:
                return collection_name

        # Enhanced semantic matching using data profile
        best_match = None
        best_score = 0

        for collection_name, profile in self.data_profile.items():
            score = 0

            # Business context matching
            business_context = profile.get('business_context', {})
            domain = business_context.get('domain', '')
            entity_type = business_context.get('entity_type', '')

            # Domain-based scoring
            if domain in query_lower:
                score += 3
            if entity_type in query_lower:
                score += 2

            # Field analysis matching
            field_analysis = profile.get('field_analysis', {})
            for field_name, field_info in field_analysis.items():
                if field_name.lower() in query_lower:
                    score += 2

                # Check sample values for semantic matching
                sample_values = field_info.get('sample_values', [])
                for sample in sample_values:
                    if isinstance(sample, str) and sample.lower() in query_lower:
                        score += 1

            # Relationship-based matching
            relationships = profile.get('relationships', {})
            for rel_field, rel_info in relationships.items():
                target_collection = rel_info.get('target_collection', '')
                if target_collection.lower() in query_lower:
                    score += 1

            if score > best_score:
                best_score = score
                best_match = collection_name

        return best_match
    
    def extract_field_and_value(self, query: str, collection_name: str) -> Tuple[Optional[str], Optional[str], Any]:
        """Enhanced field and value extraction using data profile"""
        query_lower = query.lower()

        if collection_name not in self.data_profile:
            return None, '$eq', None

        profile = self.data_profile[collection_name]
        field_analysis = profile.get('field_analysis', {})

        # Enhanced field matching
        field = None
        field_confidence = 0

        for field_name, field_info in field_analysis.items():
            confidence = 0

            # Direct field name match
            if field_name.lower() in query_lower:
                confidence += 3

            # Sample value matching for better semantic understanding
            sample_values = field_info.get('sample_values', [])
            for sample in sample_values:
                if isinstance(sample, str) and sample.lower() in query_lower:
                    confidence += 2
                    break

            # Pattern-based matching
            patterns = field_info.get('patterns', [])
            if 'Email' in patterns and 'email' in query_lower:
                confidence += 2
            elif 'FirstName LastName' in patterns and any(word in query_lower for word in ['name', 'person']):
                confidence += 2

            if confidence > field_confidence:
                field_confidence = confidence
                field = field_name

        # Enhanced operator and value extraction
        operator = '$eq'  # default
        value = None

        for op_text, op_symbol in self.operators.items():
            if op_text in query_lower:
                operator = op_symbol
                # Extract value after operator
                parts = query_lower.split(op_text)
                if len(parts) > 1:
                    value_text = parts[1].strip()

                    # Smart value conversion based on field type
                    if field and field in field_analysis:
                        field_info = field_analysis[field]
                        primary_type = field_info.get('primary_type', 'str')

                        if primary_type in ['int', 'float']:
                            try:
                                if '.' in value_text:
                                    value = float(re.findall(r'\d+\.?\d*', value_text)[0])
                                else:
                                    value = int(re.findall(r'\d+', value_text)[0])
                            except:
                                value = value_text.split()[0]
                        else:
                            # String value - check against sample values for exact matches
                            sample_values = field_info.get('sample_values', [])
                            value_words = value_text.split()

                            # Try to find exact match in sample values
                            for sample in sample_values:
                                if isinstance(sample, str):
                                    for word in value_words:
                                        if word.lower() in sample.lower():
                                            value = sample
                                            break
                                    if value:
                                        break

                            if not value:
                                value = re.sub(r'["\']', '', value_text.split()[0])
                    else:
                        # Fallback to original logic
                        try:
                            if '.' in value_text:
                                value = float(re.findall(r'\d+\.?\d*', value_text)[0])
                            else:
                                value = int(re.findall(r'\d+', value_text)[0])
                        except:
                            value = re.sub(r'["\']', '', value_text.split()[0])
                break

        return field, operator, value
    
    def process_query(self, query: str) -> Dict[str, Any]:
        """Process natural language query"""
        start_time = time.time()
        
        try:
            # Step 1: Detect intent
            intent = self.detect_intent(query)
            
            # Step 2: Find collection
            collection_name = self.find_collection(query)
            if not collection_name:
                return {
                    'success': False,
                    'message': f"❌ Couldn't find a relevant collection. Available: {', '.join(self.schema_cache.keys())}",
                    'suggestions': list(self.schema_cache.keys())
                }
            
            collection = self.db[collection_name]

            # Step 3: Build and execute query based on intent
            if intent == 'count':
                result = self._handle_count_query(query, collection, collection_name)
            elif intent in ['max', 'min']:
                result = self._handle_minmax_query(query, collection, collection_name, intent)
            else:  # find
                result = self._handle_find_query(query, collection, collection_name)
            
            result['execution_time'] = time.time() - start_time
            result['collection'] = collection_name
            result['intent'] = intent
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f"❌ Query failed: {str(e)}",
                'execution_time': time.time() - start_time
            }
    
    def _handle_count_query(self, query: str, collection, collection_name: str) -> Dict[str, Any]:
        """Handle count queries"""
        field, operator, value = self.extract_field_and_value(query, collection_name)
        
        if field and value is not None:
            # Count with condition
            query_filter = {field: {operator: value}}
            count = collection.count_documents(query_filter)
            mongodb_query = f"db.{collection.name}.countDocuments({json.dumps(query_filter)})"
        else:
            # Count all
            count = collection.count_documents({})
            mongodb_query = f"db.{collection.name}.countDocuments({{}})"
        
        return {
            'success': True,
            'message': f"✅ Found **{count}** documents in {collection.name}",
            'count': count,
            'mongodb_query': mongodb_query
        }
    
    def _handle_find_query(self, query: str, collection, collection_name: str) -> Dict[str, Any]:
        """Handle find queries"""
        field, operator, value = self.extract_field_and_value(query, collection_name)
        
        if field and value is not None:
            # Find with condition
            query_filter = {field: {operator: value}}
        else:
            # Find all
            query_filter = {}
        
        # Execute query
        cursor = collection.find(query_filter).limit(10)
        results = list(cursor)
        total_count = collection.count_documents(query_filter)
        
        mongodb_query = f"db.{collection.name}.find({json.dumps(query_filter)}).limit(10)"
        
        return {
            'success': True,
            'message': f"✅ Found **{total_count}** documents in {collection.name}" + 
                      (f" (showing first 10)" if total_count > 10 else ""),
            'results': results,
            'count': total_count,
            'mongodb_query': mongodb_query
        }
    
    def _handle_minmax_query(self, query: str, collection, collection_name: str, intent: str) -> Dict[str, Any]:
        """Handle min/max queries"""
        field, _, _ = self.extract_field_and_value(query, collection_name)

        if not field:
            # Try to find numeric field using data profile
            if collection_name in self.data_profile:
                field_analysis = self.data_profile[collection_name].get('field_analysis', {})
                for field_name, field_info in field_analysis.items():
                    if field_info.get('primary_type') in ['int', 'float']:
                        field = field_name
                        break
        
        if not field:
            return {
                'success': False,
                'message': f"❌ Couldn't find a numeric field for {intent} operation"
            }
        
        # Build aggregation pipeline
        sort_order = -1 if intent == 'max' else 1
        pipeline = [
            {'$sort': {field: sort_order}},
            {'$limit': 1}
        ]
        
        result = list(collection.aggregate(pipeline))
        mongodb_query = f"db.{collection.name}.aggregate({json.dumps(pipeline)})"
        
        if result:
            doc = result[0]
            value = doc.get(field, 'N/A')
            return {
                'success': True,
                'message': f"✅ {intent.title()} {field}: **{value}**",
                'results': result,
                'mongodb_query': mongodb_query
            }
        else:
            return {
                'success': False,
                'message': f"❌ No results found for {intent} {field}"
            }

# Initialize session state
if 'query_engine' not in st.session_state:
    st.session_state.query_engine = EnhancedQueryEngine()
if 'connected' not in st.session_state:
    st.session_state.connected = False
if 'query_history' not in st.session_state:
    st.session_state.query_history = []

# Main UI
st.title("🧠 Enhanced MongoDB Query System")
st.markdown("**MongoDB-first approach with deep data understanding - Intelligent & Accurate**")

# Sidebar for connection
with st.sidebar:
    st.header("🔗 Connection")
    
    if not st.session_state.connected:
        uri = st.text_input("MongoDB URI", 
                           value="mongodb+srv://username:<EMAIL>/",
                           type="password")
        database = st.text_input("Database Name", value="")
        
        if st.button("Connect"):
            if uri and database:
                if st.session_state.query_engine.connect(uri, database):
                    st.session_state.connected = True
                    st.success("✅ Connected!")
                    st.rerun()
    else:
        st.success("✅ Connected to MongoDB")
        if st.button("Disconnect"):
            st.session_state.connected = False
            st.session_state.query_engine = EnhancedQueryEngine()
            st.rerun()
        
        # Show enhanced data profile
        if st.session_state.query_engine.data_profile:
            st.subheader("🧠 Intelligent Data Profile")
            for name, profile in st.session_state.query_engine.data_profile.items():
                basic_info = profile.get('basic_info', {})
                business_context = profile.get('business_context', {})

                # Collection summary
                doc_count = basic_info.get('total_documents', 0)
                domain = business_context.get('domain', 'general')
                entity_type = business_context.get('entity_type', 'entity')

                st.write(f"**{name}** ({doc_count} docs)")
                st.caption(f"Domain: {domain} | Type: {entity_type}")

                with st.expander(f"🔍 Deep Analysis: {name}"):
                    # Field analysis
                    field_analysis = profile.get('field_analysis', {})
                    st.write("**Key Fields:**")
                    for field_name, field_info in list(field_analysis.items())[:5]:
                        field_type = field_info.get('primary_type', 'unknown')
                        is_categorical = field_info.get('is_categorical', False)
                        cardinality = field_info.get('cardinality', 0)

                        category_info = " (categorical)" if is_categorical else f" ({cardinality} unique)"
                        st.write(f"  • {field_name}: {field_type}{category_info}")

                    # Business rules
                    business_rules = business_context.get('business_rules', {})
                    if business_rules:
                        st.write("**Business Rules:**")
                        for rule_name, rule_desc in business_rules.items():
                            st.write(f"  • {rule_name}: {rule_desc}")

                    # Relationships
                    relationships = profile.get('relationships', {})
                    if relationships:
                        st.write("**Relationships:**")
                        for field, rel_info in relationships.items():
                            target = rel_info.get('target_collection', 'unknown')
                            st.write(f"  • {field} → {target}")

            # Query suggestions
            st.subheader("💡 Smart Suggestions")
            if st.session_state.query_engine.profiler:
                selected_collection = st.selectbox(
                    "Get suggestions for:",
                    list(st.session_state.query_engine.data_profile.keys())
                )

                if selected_collection:
                    suggestions = st.session_state.query_engine.profiler.get_query_suggestions(selected_collection)
                    for suggestion in suggestions[:5]:
                        if st.button(f"💬 {suggestion}", key=f"suggest_{suggestion}"):
                            st.session_state.suggested_query = suggestion

# Main query interface
if st.session_state.connected:
    st.subheader("💬 Ask Your Database")
    
    # Query examples
    with st.expander("💡 Example Queries"):
        st.markdown("""
        **Count Queries:**
        - How many customers are there?
        - Count all products
        - Total number of orders
        
        **Find Queries:**
        - Show me all customers
        - List products with price greater than 100
        - Find customers with age less than 30
        
        **Analytics:**
        - What's the highest price?
        - Show me the oldest customer
        - Find the minimum order amount
        """)
    
    # Query input with suggestion support
    default_query = st.session_state.get('suggested_query', '')
    query = st.text_input("Enter your question:",
                         value=default_query,
                         placeholder="How many customers do we have?")

    # Clear suggestion after use
    if 'suggested_query' in st.session_state:
        del st.session_state.suggested_query
    
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        if st.button("🚀 Execute", type="primary"):
            if query:
                with st.spinner("Processing..."):
                    result = st.session_state.query_engine.process_query(query)
                    st.session_state.query_history.append({
                        'query': query,
                        'result': result,
                        'timestamp': datetime.now()
                    })
    
    with col2:
        if st.button("🧹 Clear"):
            st.rerun()
    
    # Display results
    if st.session_state.query_history:
        latest = st.session_state.query_history[-1]
        result = latest['result']
        
        st.subheader("📋 Results")
        
        # Show main message
        if result['success']:
            st.success(result['message'])
            
            # Show data if available
            if 'results' in result and result['results']:
                st.subheader("📄 Data")
                for i, doc in enumerate(result['results'][:5], 1):
                    with st.expander(f"Document {i}"):
                        st.json(doc)
            
            # Show MongoDB query
            if 'mongodb_query' in result:
                with st.expander("🔍 MongoDB Query"):
                    st.code(result['mongodb_query'], language='javascript')
            
            # Show performance
            if 'execution_time' in result:
                st.info(f"⚡ Executed in {result['execution_time']:.3f} seconds")
        else:
            st.error(result['message'])
            if 'suggestions' in result:
                st.info(f"💡 Available collections: {', '.join(result['suggestions'])}")

else:
    st.info("👈 Please connect to your MongoDB database first")
    
    # Show demo info
    st.subheader("🧠 Enhanced Intelligence Features")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **🚀 Deep Data Understanding:**
        - Comprehensive data profiling
        - Relationship detection
        - Business context inference
        - Semantic field matching
        - Value pattern recognition
        """)

    with col2:
        st.markdown("""
        **🎯 Intelligent Query Processing:**
        - Context-aware field detection
        - Smart value matching
        - Business rule integration
        - Cross-collection relationships
        - Predictable & accurate results
        """)
