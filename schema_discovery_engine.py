"""
Intelligent Schema Discovery Engine for MongoDB
Uses AI-powered analysis to understand document structures and relationships
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics
import logging
from enum import Enum
import numpy as np
from mongodb_connection_manager import EnhancedMongoConnectionManager

logger = logging.getLogger(__name__)

class FieldSemanticType(Enum):
    """Semantic types for fields based on content analysis"""
    ID = "id"
    NAME = "name"
    EMAIL = "email"
    PHONE = "phone"
    DATE = "date"
    TIMESTAMP = "timestamp"
    URL = "url"
    CURRENCY = "currency"
    PERCENTAGE = "percentage"
    RATING = "rating"
    DESCRIPTION = "description"
    CATEGORY = "category"
    STATUS = "status"
    LOCATION = "location"
    UNKNOWN = "unknown"

@dataclass
class FieldPattern:
    """Pattern analysis for a field"""
    regex_patterns: List[str] = field(default_factory=list)
    common_values: List[Any] = field(default_factory=list)
    value_distribution: Dict[str, int] = field(default_factory=dict)
    min_length: int = 0
    max_length: int = 0
    avg_length: float = 0.0
    null_percentage: float = 0.0

@dataclass
class FieldAnalysis:
    """Comprehensive analysis of a field"""
    name: str
    data_type: str
    semantic_type: FieldSemanticType
    patterns: FieldPattern
    sample_values: List[Any] = field(default_factory=list)
    frequency: float = 0.0  # How often field appears
    uniqueness: float = 0.0  # Percentage of unique values
    relationships: List[str] = field(default_factory=list)
    confidence_score: float = 0.0

@dataclass
class CollectionSchema:
    """Inferred schema for a collection"""
    database: str
    collection: str
    fields: Dict[str, FieldAnalysis] = field(default_factory=dict)
    document_count: int = 0
    sample_size: int = 0
    schema_confidence: float = 0.0
    relationships: List[Dict[str, Any]] = field(default_factory=list)
    business_context: str = ""
    last_analyzed: datetime = field(default_factory=datetime.now)

class SchemaDiscoveryEngine:
    """
    AI-powered schema discovery engine that:
    1. Samples documents intelligently
    2. Analyzes field patterns and types
    3. Infers semantic meaning
    4. Discovers relationships between collections
    5. Builds queryable schema knowledge
    """
    
    def __init__(self, connection_manager: EnhancedMongoConnectionManager):
        self.connection_manager = connection_manager
        self.discovered_schemas: Dict[str, CollectionSchema] = {}
        self.field_semantic_patterns = self._initialize_semantic_patterns()
        self.relationship_cache: Dict[str, List[str]] = {}
        
    def _initialize_semantic_patterns(self) -> Dict[FieldSemanticType, List[str]]:
        """Initialize regex patterns for semantic type detection"""
        return {
            FieldSemanticType.EMAIL: [
                r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            ],
            FieldSemanticType.PHONE: [
                r'^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$',
                r'^\+?[1-9]\d{1,14}$'
            ],
            FieldSemanticType.URL: [
                r'^https?://[^\s/$.?#].[^\s]*$'
            ],
            FieldSemanticType.DATE: [
                r'^\d{4}-\d{2}-\d{2}$',
                r'^\d{2}/\d{2}/\d{4}$'
            ],
            FieldSemanticType.CURRENCY: [
                r'^\$?\d+\.?\d{0,2}$'
            ]
        }

    async def discover_collection_schema(self, database: str, collection: str, 
                                       sample_size: int = 1000) -> CollectionSchema:
        """Discover and analyze schema for a specific collection"""
        try:
            logger.info(f"🔍 Discovering schema for {database}.{collection}")
            
            # Get collection reference
            db = self.connection_manager.client[database]
            coll = db[collection]
            
            # Get document count
            doc_count = await asyncio.get_event_loop().run_in_executor(
                None, coll.estimated_document_count
            )
            
            if doc_count == 0:
                logger.warning(f"Collection {database}.{collection} is empty")
                return CollectionSchema(database=database, collection=collection)
            
            # Smart sampling strategy
            sample_docs = await self._smart_sample_documents(coll, doc_count, sample_size)
            
            # Analyze field patterns
            field_analyses = await self._analyze_fields(sample_docs)
            
            # Infer relationships
            relationships = await self._infer_relationships(database, collection, field_analyses)
            
            # Calculate schema confidence
            confidence = self._calculate_schema_confidence(field_analyses, len(sample_docs))
            
            # Create schema object
            schema = CollectionSchema(
                database=database,
                collection=collection,
                fields=field_analyses,
                document_count=doc_count,
                sample_size=len(sample_docs),
                schema_confidence=confidence,
                relationships=relationships,
                business_context=self._infer_business_context(collection, field_analyses)
            )
            
            # Cache the schema
            schema_key = f"{database}.{collection}"
            self.discovered_schemas[schema_key] = schema
            
            logger.info(f"✅ Schema discovered for {database}.{collection} "
                       f"(confidence: {confidence:.2f})")
            
            return schema
            
        except Exception as e:
            logger.error(f"Error discovering schema for {database}.{collection}: {str(e)}")
            raise

    async def _smart_sample_documents(self, collection, doc_count: int, 
                                    target_sample_size: int) -> List[Dict[str, Any]]:
        """Intelligently sample documents to get representative schema"""
        try:
            # Adjust sample size based on collection size
            actual_sample_size = min(target_sample_size, max(10, doc_count // 10))
            
            # Use multiple sampling strategies for better coverage
            samples = []
            
            # Strategy 1: Random sampling from beginning
            loop = asyncio.get_event_loop()
            first_batch = await loop.run_in_executor(
                None, 
                lambda: list(collection.find().limit(actual_sample_size // 2))
            )
            samples.extend(first_batch)
            
            # Strategy 2: Random sampling from middle/end
            if doc_count > actual_sample_size:
                skip_amount = max(1, doc_count // 3)
                second_batch = await loop.run_in_executor(
                    None,
                    lambda: list(collection.find().skip(skip_amount).limit(actual_sample_size // 2))
                )
                samples.extend(second_batch)
            
            logger.info(f"Sampled {len(samples)} documents from {doc_count} total")
            return samples
            
        except Exception as e:
            logger.error(f"Error sampling documents: {str(e)}")
            return []

    async def _analyze_fields(self, documents: List[Dict[str, Any]]) -> Dict[str, FieldAnalysis]:
        """Analyze all fields across sampled documents"""
        field_stats = defaultdict(lambda: {
            'types': Counter(),
            'values': [],
            'lengths': [],
            'null_count': 0,
            'total_count': 0
        })
        
        # Collect field statistics
        for doc in documents:
            self._analyze_document_fields(doc, field_stats, "")
        
        # Convert to FieldAnalysis objects
        field_analyses = {}
        for field_name, stats in field_stats.items():
            analysis = await self._create_field_analysis(field_name, stats)
            field_analyses[field_name] = analysis
        
        return field_analyses

    def _analyze_document_fields(self, doc: Dict[str, Any], field_stats: Dict, prefix: str):
        """Recursively analyze fields in a document"""
        for key, value in doc.items():
            field_path = f"{prefix}.{key}" if prefix else key
            
            field_stats[field_path]['total_count'] += 1
            
            if value is None:
                field_stats[field_path]['null_count'] += 1
            else:
                # Record type
                value_type = type(value).__name__
                field_stats[field_path]['types'][value_type] += 1
                
                # Record value for pattern analysis
                field_stats[field_path]['values'].append(value)
                
                # Record length for strings
                if isinstance(value, str):
                    field_stats[field_path]['lengths'].append(len(value))
                
                # Recursively analyze nested objects
                if isinstance(value, dict):
                    self._analyze_document_fields(value, field_stats, field_path)
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    # Analyze first few items in array of objects
                    for item in value[:3]:
                        if isinstance(item, dict):
                            self._analyze_document_fields(item, field_stats, f"{field_path}[]")

    async def _create_field_analysis(self, field_name: str, stats: Dict) -> FieldAnalysis:
        """Create comprehensive field analysis"""
        # Determine primary data type
        most_common_type = stats['types'].most_common(1)[0][0] if stats['types'] else 'unknown'
        
        # Calculate frequency
        frequency = (stats['total_count'] - stats['null_count']) / stats['total_count'] if stats['total_count'] > 0 else 0
        
        # Calculate null percentage
        null_percentage = stats['null_count'] / stats['total_count'] if stats['total_count'] > 0 else 0
        
        # Analyze patterns
        patterns = self._analyze_field_patterns(stats['values'], stats['lengths'])
        
        # Infer semantic type
        semantic_type = self._infer_semantic_type(field_name, stats['values'], most_common_type)
        
        # Calculate uniqueness
        unique_values = len(set(str(v) for v in stats['values']))
        uniqueness = unique_values / len(stats['values']) if stats['values'] else 0
        
        # Calculate confidence score
        confidence = self._calculate_field_confidence(frequency, uniqueness, semantic_type)
        
        return FieldAnalysis(
            name=field_name,
            data_type=most_common_type,
            semantic_type=semantic_type,
            patterns=patterns,
            sample_values=stats['values'][:10],  # Keep first 10 samples
            frequency=frequency,
            uniqueness=uniqueness,
            confidence_score=confidence
        )

    def _analyze_field_patterns(self, values: List[Any], lengths: List[int]) -> FieldPattern:
        """Analyze patterns in field values"""
        patterns = FieldPattern()
        
        if not values:
            return patterns
        
        # String length analysis
        if lengths:
            patterns.min_length = min(lengths)
            patterns.max_length = max(lengths)
            patterns.avg_length = statistics.mean(lengths)
        
        # Value distribution for categorical data
        if len(values) > 0:
            value_counts = Counter(str(v) for v in values)
            patterns.value_distribution = dict(value_counts.most_common(10))
            patterns.common_values = list(value_counts.keys())[:5]
        
        return patterns

    def _infer_semantic_type(self, field_name: str, values: List[Any], 
                           data_type: str) -> FieldSemanticType:
        """Infer semantic meaning of a field"""
        field_lower = field_name.lower()
        
        # Name-based inference
        if any(keyword in field_lower for keyword in ['id', '_id']):
            return FieldSemanticType.ID
        elif any(keyword in field_lower for keyword in ['name', 'title']):
            return FieldSemanticType.NAME
        elif 'email' in field_lower:
            return FieldSemanticType.EMAIL
        elif any(keyword in field_lower for keyword in ['phone', 'mobile']):
            return FieldSemanticType.PHONE
        elif any(keyword in field_lower for keyword in ['date', 'time', 'created', 'updated']):
            return FieldSemanticType.DATE
        elif any(keyword in field_lower for keyword in ['url', 'link']):
            return FieldSemanticType.URL
        elif any(keyword in field_lower for keyword in ['price', 'cost', 'amount']):
            return FieldSemanticType.CURRENCY
        elif any(keyword in field_lower for keyword in ['rating', 'score']):
            return FieldSemanticType.RATING
        elif any(keyword in field_lower for keyword in ['description', 'desc', 'summary']):
            return FieldSemanticType.DESCRIPTION
        elif any(keyword in field_lower for keyword in ['category', 'type', 'status']):
            return FieldSemanticType.CATEGORY
        
        # Pattern-based inference for string values
        if data_type == 'str' and values:
            sample_values = [str(v) for v in values[:10] if v is not None]
            
            for semantic_type, patterns in self.field_semantic_patterns.items():
                for pattern in patterns:
                    matches = sum(1 for v in sample_values if re.match(pattern, v))
                    if matches / len(sample_values) > 0.7:  # 70% match threshold
                        return semantic_type
        
        return FieldSemanticType.UNKNOWN

    async def _infer_relationships(self, database: str, collection: str, 
                                 field_analyses: Dict[str, FieldAnalysis]) -> List[Dict[str, Any]]:
        """Infer relationships between collections"""
        relationships = []
        
        # Look for potential foreign key relationships
        for field_name, analysis in field_analyses.items():
            if analysis.semantic_type == FieldSemanticType.ID and field_name != '_id':
                # This might be a foreign key
                potential_ref = self._guess_referenced_collection(field_name, database)
                if potential_ref:
                    relationships.append({
                        'type': 'foreign_key',
                        'field': field_name,
                        'references': potential_ref,
                        'confidence': 0.7
                    })
        
        return relationships

    def _guess_referenced_collection(self, field_name: str, database: str) -> Optional[str]:
        """Guess which collection a field might reference"""
        # Simple heuristic: if field is "user_id", it might reference "users" collection
        if field_name.endswith('_id'):
            potential_collection = field_name[:-3] + 's'  # user_id -> users
            
            # Check if this collection exists
            available_collections = self.connection_manager.get_collection_list(database)
            if potential_collection in available_collections:
                return f"{database}.{potential_collection}"
        
        return None

    def _calculate_field_confidence(self, frequency: float, uniqueness: float, 
                                  semantic_type: FieldSemanticType) -> float:
        """Calculate confidence score for field analysis"""
        base_score = frequency * 0.4 + (1 - abs(uniqueness - 0.5)) * 0.3
        
        # Boost confidence for recognized semantic types
        if semantic_type != FieldSemanticType.UNKNOWN:
            base_score += 0.3
        
        return min(1.0, base_score)

    def _calculate_schema_confidence(self, field_analyses: Dict[str, FieldAnalysis], 
                                   sample_size: int) -> float:
        """Calculate overall schema confidence"""
        if not field_analyses:
            return 0.0
        
        avg_field_confidence = statistics.mean(
            analysis.confidence_score for analysis in field_analyses.values()
        )
        
        # Adjust based on sample size
        sample_factor = min(1.0, sample_size / 100)  # Full confidence at 100+ samples
        
        return avg_field_confidence * sample_factor

    def _infer_business_context(self, collection_name: str, 
                              field_analyses: Dict[str, FieldAnalysis]) -> str:
        """Infer business context from collection name and fields"""
        collection_lower = collection_name.lower()
        
        # Common business contexts
        if any(keyword in collection_lower for keyword in ['user', 'customer', 'account']):
            return "User Management"
        elif any(keyword in collection_lower for keyword in ['product', 'item', 'inventory']):
            return "Product Catalog"
        elif any(keyword in collection_lower for keyword in ['order', 'purchase', 'transaction']):
            return "E-commerce"
        elif any(keyword in collection_lower for keyword in ['movie', 'film', 'video']):
            return "Media Content"
        elif any(keyword in collection_lower for keyword in ['chat', 'message', 'session']):
            return "Communication"
        elif any(keyword in collection_lower for keyword in ['log', 'event', 'audit']):
            return "System Logging"
        
        return "General Data"

    def get_schema_summary(self, database: str, collection: str) -> Optional[Dict[str, Any]]:
        """Get human-readable schema summary"""
        schema_key = f"{database}.{collection}"
        schema = self.discovered_schemas.get(schema_key)
        
        if not schema:
            return None
        
        return {
            'collection': f"{database}.{collection}",
            'document_count': schema.document_count,
            'fields_analyzed': len(schema.fields),
            'confidence': schema.schema_confidence,
            'business_context': schema.business_context,
            'key_fields': [
                {
                    'name': field.name,
                    'type': field.data_type,
                    'semantic_type': field.semantic_type.value,
                    'frequency': field.frequency
                }
                for field in sorted(schema.fields.values(), 
                                  key=lambda x: x.frequency, reverse=True)[:5]
            ],
            'relationships': schema.relationships
        }
