"""
Natural Language to MongoDB Query Engine
Converts natural language questions into MongoDB queries using AI
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import openai
import os
from dotenv import load_dotenv

from mongodb_connection_manager import EnhancedMongoConnectionManager
from metadata_storage_engine import MetadataStorageEngine

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    """Types of query intents"""
    FIND = "find"           # Find/retrieve documents
    COUNT = "count"         # Count documents
    AGGREGATE = "aggregate" # Complex aggregation
    DISTINCT = "distinct"   # Get distinct values
    SORT = "sort"          # Sort results
    LIMIT = "limit"        # Limit results
    GROUP = "group"        # Group by fields
    FILTER = "filter"      # Filter with conditions
    UNKNOWN = "unknown"

class FieldOperator(Enum):
    """MongoDB field operators"""
    EQUALS = "$eq"
    NOT_EQUALS = "$ne"
    GREATER_THAN = "$gt"
    GREATER_EQUAL = "$gte"
    LESS_THAN = "$lt"
    LESS_EQUAL = "$lte"
    IN = "$in"
    NOT_IN = "$nin"
    EXISTS = "$exists"
    REGEX = "$regex"
    TEXT_SEARCH = "$text"

@dataclass
class QueryCondition:
    """Represents a query condition"""
    field: str
    operator: FieldOperator
    value: Any
    confidence: float = 0.0

@dataclass
class ParsedQuery:
    """Parsed natural language query"""
    original_query: str
    intent: QueryIntent
    target_collections: List[str] = field(default_factory=list)
    conditions: List[QueryCondition] = field(default_factory=list)
    sort_fields: List[Tuple[str, int]] = field(default_factory=list)  # (field, direction)
    limit: Optional[int] = None
    group_by: List[str] = field(default_factory=list)
    select_fields: List[str] = field(default_factory=list)
    confidence: float = 0.0
    reasoning: str = ""

@dataclass
class MongoQuery:
    """Generated MongoDB query"""
    database: str
    collection: str
    operation: str  # find, aggregate, count, etc.
    query: Dict[str, Any]
    pipeline: List[Dict[str, Any]] = field(default_factory=list)
    options: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    explanation: str = ""

class NLToMongoDBEngine:
    """
    Natural Language to MongoDB Query Engine that:
    1. Parses natural language queries
    2. Identifies intent and entities
    3. Maps to relevant collections and fields
    4. Generates MongoDB queries
    5. Provides explanations and confidence scores
    """
    
    def __init__(self, metadata_engine: MetadataStorageEngine):
        self.metadata_engine = metadata_engine
        self.openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Query patterns for intent recognition
        self.intent_patterns = {
            QueryIntent.FIND: [
                r'\b(show|find|get|retrieve|list|display)\b',
                r'\bwhat are\b', r'\bwhich\b', r'\btell me about\b'
            ],
            QueryIntent.COUNT: [
                r'\b(count|how many|number of)\b',
                r'\btotal\b.*\b(records|documents|items)\b'
            ],
            QueryIntent.AGGREGATE: [
                r'\b(average|avg|sum|total|maximum|max|minimum|min)\b',
                r'\bgroup by\b', r'\baggregate\b'
            ],
            QueryIntent.DISTINCT: [
                r'\b(unique|distinct|different)\b.*\b(values|items)\b'
            ]
        }
        
        # Common field mappings
        self.field_synonyms = {
            'name': ['name', 'title', 'label'],
            'email': ['email', 'mail', 'email_address'],
            'rating': ['rating', 'score', 'stars', 'review'],
            'price': ['price', 'cost', 'amount', 'value'],
            'date': ['date', 'time', 'created', 'updated', 'timestamp'],
            'id': ['id', 'identifier', '_id']
        }
        
        # Operator mappings
        self.operator_patterns = {
            FieldOperator.GREATER_THAN: [r'\b(greater than|more than|above|over)\b', r'>'],
            FieldOperator.LESS_THAN: [r'\b(less than|below|under)\b', r'<'],
            FieldOperator.EQUALS: [r'\b(equals?|is|are)\b', r'='],
            FieldOperator.NOT_EQUALS: [r'\b(not|isn\'t|aren\'t)\b'],
            FieldOperator.IN: [r'\b(in|among|one of)\b'],
            FieldOperator.REGEX: [r'\b(contains?|includes?|like)\b']
        }

    async def process_query(self, query: str) -> MongoQuery:
        """Process natural language query and generate MongoDB query"""
        try:
            logger.info(f"Processing query: '{query}'")
            
            # Step 1: Parse the natural language query
            parsed_query = await self._parse_natural_language(query)
            
            # Step 2: Find relevant collections and fields
            await self._resolve_collections_and_fields(parsed_query)
            
            # Step 3: Generate MongoDB query
            mongo_query = await self._generate_mongodb_query(parsed_query)
            
            logger.info(f"Generated MongoDB query with confidence: {mongo_query.confidence:.2f}")
            return mongo_query
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            raise

    async def _parse_natural_language(self, query: str) -> ParsedQuery:
        """Parse natural language query using AI and patterns"""
        try:
            # Use OpenAI to understand the query
            ai_analysis = await self._analyze_with_ai(query)
            
            # Combine AI analysis with pattern matching
            intent = self._detect_intent(query)
            
            parsed_query = ParsedQuery(
                original_query=query,
                intent=intent,
                confidence=0.7,  # Base confidence
                reasoning=ai_analysis.get('reasoning', '')
            )
            
            # Extract conditions using patterns and AI
            parsed_query.conditions = self._extract_conditions(query, ai_analysis)
            
            # Extract other query elements
            parsed_query.limit = self._extract_limit(query)
            parsed_query.sort_fields = self._extract_sort_fields(query)
            parsed_query.group_by = self._extract_group_by(query)
            
            return parsed_query
            
        except Exception as e:
            logger.error(f"Error parsing natural language: {str(e)}")
            # Fallback to basic parsing
            return ParsedQuery(
                original_query=query,
                intent=QueryIntent.FIND,
                confidence=0.3
            )

    async def _analyze_with_ai(self, query: str) -> Dict[str, Any]:
        """Use OpenAI to analyze the query"""
        try:
            prompt = f"""
            Analyze this database query and extract the following information:
            
            Query: "{query}"
            
            Please provide a JSON response with:
            1. intent: The main intent (find, count, aggregate, distinct, etc.)
            2. entities: List of entities mentioned (users, movies, products, etc.)
            3. conditions: List of conditions with field, operator, and value
            4. sort_requirements: Any sorting requirements
            5. limit_requirements: Any limit requirements
            6. reasoning: Brief explanation of your analysis
            
            Focus on database operations and be specific about field names and operators.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a database query analyzer. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except Exception as e:
            logger.warning(f"AI analysis failed: {str(e)}")
            return {"reasoning": "AI analysis unavailable", "entities": [], "conditions": []}

    def _detect_intent(self, query: str) -> QueryIntent:
        """Detect query intent using pattern matching"""
        query_lower = query.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent
        
        return QueryIntent.FIND  # Default intent

    def _extract_conditions(self, query: str, ai_analysis: Dict[str, Any]) -> List[QueryCondition]:
        """Extract query conditions from text and AI analysis"""
        conditions = []
        query_lower = query.lower()
        
        # Extract numeric conditions
        numeric_patterns = [
            (r'rating\s*(>|greater than|above)\s*(\d+(?:\.\d+)?)', 'rating', FieldOperator.GREATER_THAN),
            (r'rating\s*(<|less than|below)\s*(\d+(?:\.\d+)?)', 'rating', FieldOperator.LESS_THAN),
            (r'price\s*(>|greater than|above)\s*(\d+(?:\.\d+)?)', 'price', FieldOperator.GREATER_THAN),
            (r'price\s*(<|less than|below)\s*(\d+(?:\.\d+)?)', 'price', FieldOperator.LESS_THAN),
        ]
        
        for pattern, field, operator in numeric_patterns:
            matches = re.finditer(pattern, query_lower)
            for match in matches:
                value = float(match.group(2))
                conditions.append(QueryCondition(
                    field=field,
                    operator=operator,
                    value=value,
                    confidence=0.8
                ))
        
        # Extract text conditions
        text_patterns = [
            (r'genre\s*(is|contains?|includes?)\s*["\']?([^"\']+)["\']?', 'genres', FieldOperator.REGEX),
            (r'name\s*(is|contains?|includes?)\s*["\']?([^"\']+)["\']?', 'name', FieldOperator.REGEX),
            (r'title\s*(is|contains?|includes?)\s*["\']?([^"\']+)["\']?', 'title', FieldOperator.REGEX),
        ]
        
        for pattern, field, operator in text_patterns:
            matches = re.finditer(pattern, query_lower)
            for match in matches:
                value = match.group(2).strip()
                conditions.append(QueryCondition(
                    field=field,
                    operator=operator,
                    value=value,
                    confidence=0.7
                ))
        
        return conditions

    def _extract_limit(self, query: str) -> Optional[int]:
        """Extract limit from query"""
        patterns = [
            r'\b(top|first|limit)\s+(\d+)\b',
            r'\b(\d+)\s+(results?|items?|records?)\b'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.lower())
            if match:
                return int(match.group(2) if 'top' in match.group(1) else match.group(1))
        
        return None

    def _extract_sort_fields(self, query: str) -> List[Tuple[str, int]]:
        """Extract sort fields and directions"""
        sort_fields = []
        
        # Look for sort patterns
        if re.search(r'\b(sort|order)\s+by\s+(\w+)', query.lower()):
            match = re.search(r'\b(sort|order)\s+by\s+(\w+)', query.lower())
            field = match.group(2)
            
            # Determine direction
            direction = -1 if re.search(r'\b(desc|descending|highest|largest)\b', query.lower()) else 1
            sort_fields.append((field, direction))
        
        return sort_fields

    def _extract_group_by(self, query: str) -> List[str]:
        """Extract group by fields"""
        group_fields = []
        
        if re.search(r'\bgroup\s+by\s+(\w+)', query.lower()):
            match = re.search(r'\bgroup\s+by\s+(\w+)', query.lower())
            group_fields.append(match.group(1))
        
        return group_fields

    async def _resolve_collections_and_fields(self, parsed_query: ParsedQuery):
        """Resolve collections and fields using metadata engine"""
        try:
            # Find relevant collections
            relevant_schemas = self.metadata_engine.find_relevant_schemas(
                parsed_query.original_query, limit=3
            )
            
            parsed_query.target_collections = [schema_id for schema_id, _ in relevant_schemas]
            
            # Resolve field names
            for condition in parsed_query.conditions:
                resolved_field = await self._resolve_field_name(
                    condition.field, parsed_query.target_collections
                )
                if resolved_field:
                    condition.field = resolved_field
            
            # Resolve sort fields
            resolved_sort = []
            for field, direction in parsed_query.sort_fields:
                resolved_field = await self._resolve_field_name(field, parsed_query.target_collections)
                if resolved_field:
                    resolved_sort.append((resolved_field, direction))
            parsed_query.sort_fields = resolved_sort
            
        except Exception as e:
            logger.error(f"Error resolving collections and fields: {str(e)}")

    async def _resolve_field_name(self, field_name: str, collections: List[str]) -> Optional[str]:
        """Resolve field name using metadata and synonyms"""
        # Direct field lookup
        suggestions = self.metadata_engine.get_field_suggestions(field_name, limit=5)
        
        # Filter by target collections
        for suggestion in suggestions:
            if any(collection in suggestion['schema_id'] for collection in collections):
                return suggestion['field_name']
        
        # Try synonyms
        for canonical_field, synonyms in self.field_synonyms.items():
            if field_name.lower() in synonyms:
                suggestions = self.metadata_engine.get_field_suggestions(canonical_field, limit=5)
                for suggestion in suggestions:
                    if any(collection in suggestion['schema_id'] for collection in collections):
                        return suggestion['field_name']
        
        return field_name  # Return original if no resolution found

    async def _generate_mongodb_query(self, parsed_query: ParsedQuery) -> MongoQuery:
        """Generate MongoDB query from parsed query"""
        try:
            if not parsed_query.target_collections:
                raise ValueError("No target collections identified")
            
            # Use the first (most relevant) collection
            schema_id = parsed_query.target_collections[0]
            database, collection = schema_id.split('.', 1)
            
            # Build query based on intent
            if parsed_query.intent == QueryIntent.FIND:
                return self._build_find_query(database, collection, parsed_query)
            elif parsed_query.intent == QueryIntent.COUNT:
                return self._build_count_query(database, collection, parsed_query)
            elif parsed_query.intent == QueryIntent.AGGREGATE:
                return self._build_aggregate_query(database, collection, parsed_query)
            else:
                return self._build_find_query(database, collection, parsed_query)
                
        except Exception as e:
            logger.error(f"Error generating MongoDB query: {str(e)}")
            raise

    def _build_find_query(self, database: str, collection: str, parsed_query: ParsedQuery) -> MongoQuery:
        """Build a find query"""
        query_filter = {}
        
        # Build filter conditions
        for condition in parsed_query.conditions:
            if condition.operator == FieldOperator.REGEX:
                query_filter[condition.field] = {"$regex": condition.value, "$options": "i"}
            elif condition.operator == FieldOperator.GREATER_THAN:
                query_filter[condition.field] = {"$gt": condition.value}
            elif condition.operator == FieldOperator.LESS_THAN:
                query_filter[condition.field] = {"$lt": condition.value}
            else:
                query_filter[condition.field] = condition.value
        
        # Build options
        options = {}
        if parsed_query.sort_fields:
            options['sort'] = {field: direction for field, direction in parsed_query.sort_fields}
        if parsed_query.limit:
            options['limit'] = parsed_query.limit
        
        explanation = f"Find documents in {collection}"
        if query_filter:
            explanation += f" where {', '.join(query_filter.keys())}"
        if parsed_query.sort_fields:
            explanation += f" sorted by {', '.join(f[0] for f in parsed_query.sort_fields)}"
        if parsed_query.limit:
            explanation += f" limited to {parsed_query.limit} results"
        
        return MongoQuery(
            database=database,
            collection=collection,
            operation="find",
            query=query_filter,
            options=options,
            confidence=parsed_query.confidence,
            explanation=explanation
        )

    def _build_count_query(self, database: str, collection: str, parsed_query: ParsedQuery) -> MongoQuery:
        """Build a count query"""
        query_filter = {}
        
        for condition in parsed_query.conditions:
            if condition.operator == FieldOperator.REGEX:
                query_filter[condition.field] = {"$regex": condition.value, "$options": "i"}
            else:
                query_filter[condition.field] = condition.value
        
        return MongoQuery(
            database=database,
            collection=collection,
            operation="count_documents",
            query=query_filter,
            confidence=parsed_query.confidence,
            explanation=f"Count documents in {collection} matching the criteria"
        )

    def _build_aggregate_query(self, database: str, collection: str, parsed_query: ParsedQuery) -> MongoQuery:
        """Build an aggregation query"""
        pipeline = []
        
        # Add match stage if conditions exist
        if parsed_query.conditions:
            match_stage = {}
            for condition in parsed_query.conditions:
                if condition.operator == FieldOperator.REGEX:
                    match_stage[condition.field] = {"$regex": condition.value, "$options": "i"}
                else:
                    match_stage[condition.field] = condition.value
            pipeline.append({"$match": match_stage})
        
        # Add group stage if group by fields exist
        if parsed_query.group_by:
            group_stage = {"_id": {}}
            for field in parsed_query.group_by:
                group_stage["_id"][field] = f"${field}"
            group_stage["count"] = {"$sum": 1}
            pipeline.append({"$group": group_stage})
        
        # Add sort stage
        if parsed_query.sort_fields:
            sort_stage = {field: direction for field, direction in parsed_query.sort_fields}
            pipeline.append({"$sort": sort_stage})
        
        # Add limit stage
        if parsed_query.limit:
            pipeline.append({"$limit": parsed_query.limit})
        
        return MongoQuery(
            database=database,
            collection=collection,
            operation="aggregate",
            query={},
            pipeline=pipeline,
            confidence=parsed_query.confidence,
            explanation=f"Aggregate data from {collection} with {len(pipeline)} stages"
        )
