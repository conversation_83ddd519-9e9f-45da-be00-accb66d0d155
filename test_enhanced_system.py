#!/usr/bin/env python3
"""
Test script for Enhanced Intelligent Discovery System
"""

import os
import sys
from dotenv import load_dotenv
import logging
import json

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enhanced_system():
    """Test the enhanced query processing system"""
    
    mongodb_uri = os.getenv('MONGODB_URI')
    if not mongodb_uri:
        print("❌ MONGODB_URI not found in environment variables")
        return False
    
    try:
        from enhanced_query_processor import EnhancedQueryProcessor
        
        print("🚀 Testing Enhanced Query Processor...")
        print("="*50)
        
        # Initialize the processor
        processor = EnhancedQueryProcessor(mongodb_uri)
        
        # Test queries
        test_queries = [
            "Tell me about the Samsung Galaxy S24 product",
            "Show me all products",
            "How many products do we have?",
            "Find the most expensive product",
            "List all available collections"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {query}")
            print("-" * 40)
            
            try:
                result = processor.process_query(query, max_results=3)
                
                print(f"✅ Success: {result.result_count} results found")
                print(f"📊 Collection: {result.database_name}.{result.collection_name}")
                print(f"🎯 Confidence: {result.confidence_score:.2f}")
                print(f"⚡ Time: {result.execution_time:.3f}s")
                
                if result.mongodb_query:
                    print(f"🔧 MongoDB Query: {json.dumps(result.mongodb_query, indent=2)}")
                
                if result.results:
                    print(f"📄 Sample Result: {list(result.results[0].keys())}")
                
                if result.error:
                    print(f"⚠️ Error: {result.error}")
                    
            except Exception as e:
                print(f"❌ Query failed: {e}")
        
        # Test collection overview
        print(f"\n📊 Collection Overview:")
        print("-" * 40)
        overview = processor.get_collection_overview()
        print(f"Total Collections: {overview['total_collections']}")
        
        for db_name, collections in overview.get('databases', {}).items():
            print(f"\n📁 {db_name}:")
            for coll in collections:
                print(f"  📄 {coll['name']}: {coll['documents']:,} docs")
        
        print("\n✅ Enhanced system test completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please run setup_intelligent_discovery.py first")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_system()
    sys.exit(0 if success else 1)
