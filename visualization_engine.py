"""
Intelligent Visualization Engine for MongoDB Query Results
Automatically detects when to show charts and generates appropriate visualizations
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import numpy as np
from bson import ObjectId

class VisualizationEngine:
    """Intelligent visualization engine for MongoDB query results"""
    
    def __init__(self):
        # Visualization intent patterns
        self.viz_patterns = {
            'trend': [
                r'\b(trend|trends|over time|timeline|progression|growth|decline)\b',
                r'\b(monthly|daily|yearly|weekly|quarterly)\b',
                r'\b(show.*time|time.*series|historical)\b'
            ],
            'comparison': [
                r'\b(compare|comparison|vs|versus|against|between)\b',
                r'\b(top|bottom|highest|lowest|best|worst)\b',
                r'\b(rank|ranking|order by|sort by)\b'
            ],
            'distribution': [
                r'\b(distribution|spread|range|histogram|frequency)\b',
                r'\b(how many.*each|count.*by|group.*by)\b',
                r'\b(breakdown|split|segment)\b'
            ],
            'proportion': [
                r'\b(percentage|percent|proportion|share|ratio)\b',
                r'\b(pie|donut|composition|makeup)\b',
                r'\b(what.*of|how much.*of)\b'
            ],
            'correlation': [
                r'\b(relationship|correlation|related|connection)\b',
                r'\b(scatter|plot.*against|vs)\b',
                r'\b(impact|effect|influence)\b'
            ]
        }
        
        # Chart type mapping
        self.chart_types = {
            'trend': ['line', 'area'],
            'comparison': ['bar', 'column', 'horizontal_bar'],
            'distribution': ['histogram', 'bar', 'box'],
            'proportion': ['pie', 'donut', 'treemap'],
            'correlation': ['scatter', 'bubble']
        }
    
    def should_visualize(self, query: str, results: List[Dict], intent: str) -> bool:
        """Determine if query results should be visualized"""
        if not results or len(results) < 2:
            return False
        
        # Check for visualization keywords in query
        query_lower = query.lower()
        for viz_type, patterns in self.viz_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return True
        
        # Check if results are suitable for visualization
        if self._has_numeric_data(results) or self._has_categorical_data(results):
            return True
        
        # Check for aggregation results (usually good for viz)
        if len(results) <= 50 and self._looks_like_aggregation(results):
            return True
        
        return False
    
    def detect_visualization_intent(self, query: str, results: List[Dict]) -> str:
        """Detect what type of visualization is most appropriate"""
        query_lower = query.lower()
        
        # Score each visualization type
        scores = {}
        for viz_type, patterns in self.viz_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query_lower))
                score += matches
            scores[viz_type] = score
        
        # Get highest scoring intent
        if scores and max(scores.values()) > 0:
            return max(scores, key=scores.get)
        
        # Fallback based on data characteristics
        if self._has_time_data(results):
            return 'trend'
        elif self._has_categorical_counts(results):
            return 'distribution'
        elif self._has_numeric_comparison(results):
            return 'comparison'
        else:
            return 'distribution'  # Default
    
    def generate_visualization(self, query: str, results: List[Dict],
                            collection_name: str, intent: str = None) -> Optional[Dict]:
        """Generate appropriate visualization for query results"""
        if not self.should_visualize(query, results, intent):
            return None

        # Clean results for DataFrame conversion (handle ObjectId and other non-serializable types)
        cleaned_results = self._clean_results_for_viz(results)

        # Convert to DataFrame for easier processing
        df = pd.DataFrame(cleaned_results)
        
        # Detect visualization intent if not provided
        if not intent:
            viz_intent = self.detect_visualization_intent(query, results)
        else:
            viz_intent = intent
        
        # Generate visualization based on intent
        try:
            if viz_intent == 'trend':
                return self._create_trend_chart(df, query, collection_name)
            elif viz_intent == 'comparison':
                return self._create_comparison_chart(df, query, collection_name)
            elif viz_intent == 'distribution':
                return self._create_distribution_chart(df, query, collection_name)
            elif viz_intent == 'proportion':
                return self._create_proportion_chart(df, query, collection_name)
            elif viz_intent == 'correlation':
                return self._create_correlation_chart(df, query, collection_name)
            else:
                return self._create_default_chart(df, query, collection_name)
        except Exception as e:
            st.error(f"Visualization error: {e}")
            return None
    
    def _create_trend_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create trend/time series visualization"""
        # Find time column
        time_col = self._find_time_column(df)
        if not time_col:
            return self._create_default_chart(df, query, collection_name)
        
        # Find numeric column for y-axis
        numeric_col = self._find_best_numeric_column(df, exclude=[time_col])
        if not numeric_col:
            return self._create_default_chart(df, query, collection_name)
        
        # Sort by time
        df_sorted = df.sort_values(time_col)
        
        fig = px.line(df_sorted, x=time_col, y=numeric_col,
                     title=f"{numeric_col.title()} Trend Over Time",
                     labels={time_col: time_col.title(), numeric_col: numeric_col.title()})
        
        return {
            'type': 'trend',
            'figure': fig,
            'title': f"📈 {numeric_col.title()} Trend",
            'description': f"Time series showing {numeric_col} changes over {time_col}"
        }
    
    def _create_comparison_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create comparison bar chart"""
        # Find categorical column for x-axis
        cat_col = self._find_best_categorical_column(df)
        if not cat_col:
            return self._create_default_chart(df, query, collection_name)
        
        # Find numeric column for y-axis
        numeric_col = self._find_best_numeric_column(df, exclude=[cat_col])
        
        if numeric_col:
            # Numeric comparison
            df_sorted = df.sort_values(numeric_col, ascending=False).head(20)
            fig = px.bar(df_sorted, x=cat_col, y=numeric_col,
                        title=f"{numeric_col.title()} by {cat_col.title()}",
                        labels={cat_col: cat_col.title(), numeric_col: numeric_col.title()})
        else:
            # Count comparison
            counts = df[cat_col].value_counts().head(20)
            fig = px.bar(x=counts.index, y=counts.values,
                        title=f"Count by {cat_col.title()}",
                        labels={'x': cat_col.title(), 'y': 'Count'})
        
        return {
            'type': 'comparison',
            'figure': fig,
            'title': f"📊 {cat_col.title()} Comparison",
            'description': f"Comparison of values across different {cat_col}"
        }
    
    def _create_distribution_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create distribution visualization"""
        # Find best column for distribution
        numeric_col = self._find_best_numeric_column(df)
        
        if numeric_col and df[numeric_col].dtype in ['int64', 'float64']:
            # Numeric distribution - histogram
            fig = px.histogram(df, x=numeric_col, nbins=20,
                             title=f"Distribution of {numeric_col.title()}",
                             labels={numeric_col: numeric_col.title()})
        else:
            # Categorical distribution - bar chart
            cat_col = self._find_best_categorical_column(df)
            if cat_col:
                counts = df[cat_col].value_counts().head(15)
                fig = px.bar(x=counts.index, y=counts.values,
                           title=f"Distribution of {cat_col.title()}",
                           labels={'x': cat_col.title(), 'y': 'Count'})
            else:
                return None
        
        return {
            'type': 'distribution',
            'figure': fig,
            'title': f"📈 Distribution Analysis",
            'description': f"Distribution showing frequency and spread of values"
        }
    
    def _create_proportion_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create pie/donut chart for proportions"""
        cat_col = self._find_best_categorical_column(df)
        if not cat_col:
            return self._create_default_chart(df, query, collection_name)
        
        # Get value counts
        counts = df[cat_col].value_counts().head(10)
        
        fig = px.pie(values=counts.values, names=counts.index,
                    title=f"Proportion of {cat_col.title()}")
        
        return {
            'type': 'proportion',
            'figure': fig,
            'title': f"🥧 {cat_col.title()} Breakdown",
            'description': f"Proportional breakdown showing relative sizes"
        }
    
    def _create_correlation_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create scatter plot for correlation"""
        numeric_cols = [col for col in df.columns if df[col].dtype in ['int64', 'float64']]
        
        if len(numeric_cols) < 2:
            return self._create_default_chart(df, query, collection_name)
        
        x_col, y_col = numeric_cols[0], numeric_cols[1]
        
        fig = px.scatter(df, x=x_col, y=y_col,
                        title=f"{y_col.title()} vs {x_col.title()}",
                        labels={x_col: x_col.title(), y_col: y_col.title()})
        
        return {
            'type': 'correlation',
            'figure': fig,
            'title': f"🔗 Correlation Analysis",
            'description': f"Relationship between {x_col} and {y_col}"
        }
    
    def _create_default_chart(self, df: pd.DataFrame, query: str, collection_name: str) -> Dict:
        """Create default visualization when specific type can't be determined"""
        # Try to create a simple bar chart
        if len(df.columns) >= 2:
            first_col = df.columns[0]
            second_col = df.columns[1]
            
            if df[second_col].dtype in ['int64', 'float64']:
                fig = px.bar(df.head(20), x=first_col, y=second_col,
                           title=f"{second_col.title()} by {first_col.title()}")
            else:
                counts = df[first_col].value_counts().head(15)
                fig = px.bar(x=counts.index, y=counts.values,
                           title=f"Count by {first_col.title()}")
        else:
            return None
        
        return {
            'type': 'default',
            'figure': fig,
            'title': f"📊 Data Visualization",
            'description': f"Visual representation of {collection_name} data"
        }
    
    # Helper methods for data analysis
    def _has_numeric_data(self, results: List[Dict]) -> bool:
        """Check if results contain numeric data suitable for visualization"""
        if not results:
            return False
        
        sample = results[0]
        for value in sample.values():
            if isinstance(value, (int, float)) and not isinstance(value, bool):
                return True
        return False
    
    def _has_categorical_data(self, results: List[Dict]) -> bool:
        """Check if results contain categorical data"""
        if not results:
            return False
        
        sample = results[0]
        for value in sample.values():
            if isinstance(value, str) and len(value) < 100:  # Reasonable string length
                return True
        return False
    
    def _has_time_data(self, results: List[Dict]) -> bool:
        """Check if results contain time/date data"""
        if not results:
            return False
        
        sample = results[0]
        for key, value in sample.items():
            if isinstance(value, datetime):
                return True
            if isinstance(value, str) and ('date' in key.lower() or 'time' in key.lower()):
                return True
        return False
    
    def _looks_like_aggregation(self, results: List[Dict]) -> bool:
        """Check if results look like aggregation output"""
        if not results or len(results) > 100:
            return False
        
        # Check for common aggregation patterns
        sample = results[0]
        keys = list(sample.keys())
        
        # Look for _id field (common in MongoDB aggregation)
        if '_id' in keys:
            return True
        
        # Look for count/sum/avg fields
        for key in keys:
            if any(word in key.lower() for word in ['count', 'sum', 'avg', 'total', 'max', 'min']):
                return True
        
        return False
    
    def _has_categorical_counts(self, results: List[Dict]) -> bool:
        """Check if results represent categorical counts"""
        if len(results) > 50:
            return False
        
        sample = results[0]
        has_category = False
        has_count = False
        
        for key, value in sample.items():
            if isinstance(value, str):
                has_category = True
            elif isinstance(value, (int, float)):
                has_count = True
        
        return has_category and has_count
    
    def _has_numeric_comparison(self, results: List[Dict]) -> bool:
        """Check if results are suitable for numeric comparison"""
        if not results:
            return False
        
        numeric_fields = 0
        for key, value in results[0].items():
            if isinstance(value, (int, float)) and not isinstance(value, bool):
                numeric_fields += 1
        
        return numeric_fields >= 1 and len(results) <= 50
    
    def _find_time_column(self, df: pd.DataFrame) -> Optional[str]:
        """Find the best time/date column"""
        for col in df.columns:
            if df[col].dtype == 'datetime64[ns]':
                return col
            if 'date' in col.lower() or 'time' in col.lower():
                return col
        return None
    
    def _find_best_numeric_column(self, df: pd.DataFrame, exclude: List[str] = None) -> Optional[str]:
        """Find the best numeric column for visualization"""
        exclude = exclude or []
        
        for col in df.columns:
            if col in exclude:
                continue
            if df[col].dtype in ['int64', 'float64'] and col != '_id':
                return col
        return None
    
    def _find_best_categorical_column(self, df: pd.DataFrame, exclude: List[str] = None) -> Optional[str]:
        """Find the best categorical column for visualization"""
        exclude = exclude or []
        
        for col in df.columns:
            if col in exclude:
                continue
            if df[col].dtype == 'object' and col != '_id':
                # Check if it's not too many unique values
                if df[col].nunique() <= 50:
                    return col
        return None
    
    def suggest_visualization_queries(self, collection_name: str, data_profile: Dict) -> List[str]:
        """Suggest queries that would produce good visualizations"""
        suggestions = []
        
        if collection_name not in data_profile:
            return suggestions
        
        profile = data_profile[collection_name]
        field_analysis = profile.get('field_analysis', {})
        
        # Find good fields for visualization
        numeric_fields = []
        categorical_fields = []
        time_fields = []
        
        for field_name, field_info in field_analysis.items():
            field_type = field_info.get('primary_type', 'str')
            is_categorical = field_info.get('is_categorical', False)
            
            if field_type in ['int', 'float']:
                numeric_fields.append(field_name)
            elif is_categorical:
                categorical_fields.append(field_name)
            elif 'date' in field_name.lower() or 'time' in field_name.lower():
                time_fields.append(field_name)
        
        # Generate suggestions
        if categorical_fields:
            suggestions.append(f"Show distribution of {categorical_fields[0]}")
            suggestions.append(f"Compare {collection_name} by {categorical_fields[0]}")
        
        if numeric_fields:
            suggestions.append(f"Show {numeric_fields[0]} distribution")
            if categorical_fields:
                suggestions.append(f"Show average {numeric_fields[0]} by {categorical_fields[0]}")
        
        if time_fields and numeric_fields:
            suggestions.append(f"Show {numeric_fields[0]} trend over time")
        
        if len(numeric_fields) >= 2:
            suggestions.append(f"Show relationship between {numeric_fields[0]} and {numeric_fields[1]}")
        
        return suggestions[:5]  # Limit to 5 suggestions

    def _clean_results_for_viz(self, results: List[Dict]) -> List[Dict]:
        """Clean results to handle MongoDB ObjectId and other non-serializable types"""
        cleaned_results = []

        for doc in results:
            cleaned_doc = {}
            for key, value in doc.items():
                if isinstance(value, ObjectId):
                    cleaned_doc[key] = str(value)
                elif isinstance(value, datetime):
                    cleaned_doc[key] = value
                elif isinstance(value, (dict, list)):
                    # Skip complex nested structures for visualization
                    continue
                else:
                    cleaned_doc[key] = value
            cleaned_results.append(cleaned_doc)

        return cleaned_results
