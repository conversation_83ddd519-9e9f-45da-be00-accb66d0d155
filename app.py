"""
DataPilot - Intelligent MongoDB Query System
A sophisticated system for natural language querying of MongoDB databases
with intelligent collection discovery and visualization capabilities.
"""

import streamlit as st
import os
import sys
import asyncio
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# Core imports
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List, Optional
import time
import traceback
import random

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our custom modules
try:
    from core.mongodb_manager import MongoDBManager
    from core.query_engine import IntelligentQueryEngine
    from core.visualization_engine import VisualizationEngine
    from core.schema_discovery import SchemaDiscoveryEngine
    from ui.components import UIComponents
    from utils.config import config
    MODULES_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import modules: {e}")
    MODULES_AVAILABLE = False
    st.error("⚠️ Some modules are not available. Please check dependencies.")

def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'initialized' not in st.session_state:
        st.session_state.initialized = True
        st.session_state.connected = False
        st.session_state.demo_mode = False
        st.session_state.current_database = None
        st.session_state.chat_history = []
        st.session_state.query_history = []
        st.session_state.discovered_schemas = {}
        st.session_state.connection_config = {}
        st.session_state.query_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'visualizations_created': 0
        }

        # Initialize core components
        if MODULES_AVAILABLE:
            try:
                st.session_state.mongodb_manager = MongoDBManager()
                st.session_state.query_engine = IntelligentQueryEngine()
                st.session_state.viz_engine = VisualizationEngine()
                st.session_state.schema_engine = SchemaDiscoveryEngine()
                logger.info("Core components initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize components: {e}")
                st.error(f"Failed to initialize core components: {e}")
        else:
            # Create mock objects for demo
            st.session_state.mongodb_manager = None
            st.session_state.query_engine = None
            st.session_state.viz_engine = None
            st.session_state.schema_engine = None

def main():
    """Main application entry point"""

    # Page configuration
    st.set_page_config(
        page_title="DataPilot - MongoDB Intelligence",
        page_icon="🚀",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    initialize_session_state()

    # Custom CSS for better UI
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    .status-card {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f4e79;
        margin: 1rem 0;
    }
    .query-box {
        background: #ffffff;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Main header
    st.markdown("""
    <div class="main-header">
        <h1>🚀 DataPilot</h1>
        <p>Intelligent MongoDB Query System with Natural Language Processing</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar for connection and settings
    render_sidebar()

    # Main content area
    render_main_content()

    # Footer
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #666;'>"
        "DataPilot v1.0 - Intelligent Database Querying System"
        "</div>",
        unsafe_allow_html=True
    )

def render_sidebar():
    """Render the sidebar with connection settings and controls"""
    with st.sidebar:
        st.markdown("### 🔧 Connection & Settings")

        # Connection status and form
        if MODULES_AVAILABLE and st.session_state.mongodb_manager:
            mongodb_manager = st.session_state.mongodb_manager

            # Connection status
            UIComponents.render_connection_status(mongodb_manager)

            # Connection form
            if UIComponents.render_connection_form(mongodb_manager):
                # Handle connection attempt
                connection_uri = st.session_state.get('connection_uri_input', '')
                if connection_uri:
                    with st.spinner("🔗 Connecting to MongoDB..."):
                        try:
                            # Use the synchronous connect method
                            success = mongodb_manager.connect_sync(connection_uri)

                            if success:
                                st.session_state.connected = True
                                st.session_state.current_database = mongodb_manager.current_database
                                st.session_state.mongodb_uri = connection_uri
                                st.success("✅ Connected successfully!")

                                # Show connection info
                                info = mongodb_manager.connection_info
                                st.info(f"📊 Found {info.get('total_databases', 0)} databases, "
                                       f"currently using: {info.get('current_database', 'Unknown')}")

                                time.sleep(1)  # Brief pause to show success message
                                st.rerun()
                            else:
                                st.error("❌ Connection failed! Please check your MongoDB URI and ensure MongoDB is running.")

                        except Exception as e:
                            st.error(f"❌ Connection failed: {str(e)}")
                            logger.error(f"Connection error: {traceback.format_exc()}")

            # Database selection
            if mongodb_manager.is_connected:
                UIComponents.render_database_selector(mongodb_manager)

        else:
            # Fallback UI when modules not available
            st.warning("⚠️ Core modules not available")
            if st.button("🎮 Demo Mode", type="primary"):
                st.session_state.demo_mode = True
                st.session_state.connected = True
                st.rerun()

        # Query history
        selected_query = UIComponents.render_query_history(st.session_state.query_history)
        if selected_query:
            st.info(f"💡 Selected from history: {selected_query}")
            st.info("Copy the query above to the query box!")

        # Settings panel
        settings = UIComponents.render_settings_panel()

        # Update session state with settings
        for key, value in settings.items():
            st.session_state[key] = value

        # Statistics
        if st.session_state.query_stats['total_queries'] > 0:
            UIComponents.render_stats_dashboard(st.session_state.query_stats)

def render_main_content():
    """Render the main content area"""

    # Quick start guide for new users
    if not st.session_state.get('connected', False):
        render_quick_start()
        return

    # Main query interface
    render_query_interface()

    # Results and visualization area
    render_results_area()

def render_quick_start():
    """Render quick start guide for new users"""
    st.markdown("### 🚀 Welcome to DataPilot!")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        #### What is DataPilot?
        DataPilot is an intelligent MongoDB query system that allows you to:

        - 🗣️ **Ask questions in natural language**
        - 🔍 **Automatically discover relevant data**
        - 📊 **Generate visualizations automatically**
        - 🚀 **Query across multiple databases**
        - 🧠 **Learn your database structure**
        """)

    with col2:
        st.markdown("""
        #### Example Queries
        Try asking questions like:

        - "Show me all products"
        - "What are the top 10 customers by sales?"
        - "Create a chart of orders by month"
        - "Find products without prices"
        - "Count users by registration date"
        """)

    # Demo mode
    st.markdown("### 🎮 Try Demo Mode")
    if st.button("🚀 Start Demo with Sample Data", type="primary"):
        st.session_state.connected = True
        st.session_state.current_database = "demo_ecommerce"
        st.rerun()

def render_query_interface():
    """Render the main query interface"""
    # Query input
    user_query = UIComponents.render_query_input(
        "Ask questions like: 'Show me top 10 products by price' or 'Create a chart of sales by month'"
    )

    # Get query suggestions if available
    suggestions = []
    if MODULES_AVAILABLE and st.session_state.query_engine:
        try:
            schema_info = ""
            if st.session_state.mongodb_manager and st.session_state.mongodb_manager.is_connected:
                schema_info = st.session_state.mongodb_manager.get_schema_summary()
            suggestions = st.session_state.query_engine.get_query_suggestions(user_query, schema_info)
        except Exception as e:
            logger.error(f"Error getting suggestions: {e}")

    # Query action buttons
    action = UIComponents.render_query_buttons(user_query, suggestions)

    # Handle actions
    if action == "execute" and user_query.strip():
        execute_query(user_query.strip())
    elif action == "example":
        examples = [
            "Show me all products",
            "Count orders by status",
            "Top 5 customers by total orders",
            "Products without stock",
            "Average order value by month",
            "Create a pie chart of categories",
            "Find users registered this month"
        ]
        selected_example = random.choice(examples)
        # Use a different approach to set the example
        st.info(f"💡 Example query: {selected_example}")
        st.info("Copy the example above to the query box!")
    elif action == "suggest" and suggestions:
        # Show suggestions
        selected_suggestion = UIComponents.render_query_suggestions(suggestions)
        if selected_suggestion:
            st.info(f"💡 Suggested query: {selected_suggestion}")
            st.info("Copy the suggestion above to the query box!")
    elif action == "clear":
        # Clear input by deleting the session state key
        if 'main_query_input' in st.session_state:
            del st.session_state.main_query_input
        st.rerun()

def render_results_area():
    """Render the results and visualization area"""
    if st.session_state.chat_history:
        st.markdown("### 💬 Conversation History")

        for query, response, viz in st.session_state.chat_history:
            with st.container():
                st.markdown(f"**You:** {query}")
                st.markdown(f"**DataPilot:** {response}")

                if viz:
                    st.plotly_chart(viz, use_container_width=True)

                st.markdown("---")

def execute_query(user_query: str):
    """Execute a user query and display results"""
    start_time = time.time()

    try:
        # Update query stats
        st.session_state.query_stats['total_queries'] += 1

        with st.spinner("🧠 Analyzing your query..."):
            if MODULES_AVAILABLE and st.session_state.mongodb_manager and st.session_state.query_engine:
                # Real query processing
                result = process_intelligent_query(user_query)
            else:
                # Demo mode processing
                result = process_demo_query(user_query)

            # Update stats
            if result['success']:
                st.session_state.query_stats['successful_queries'] += 1
                if result.get('visualization'):
                    st.session_state.query_stats['visualizations_created'] += 1
            else:
                st.session_state.query_stats['failed_queries'] += 1

            # Display result
            UIComponents.render_chat_message("user", user_query, "👤")
            UIComponents.render_chat_message("assistant", result['response'], "🤖")

            # Show visualization if available
            if result.get('visualization'):
                st.plotly_chart(result['visualization'], use_container_width=True)

            # Show query details if enabled
            if st.session_state.get('show_query_details', False):
                UIComponents.render_query_result(result)

            # Add to chat history
            st.session_state.chat_history.append((
                user_query,
                result['response'],
                result.get('visualization')
            ))
            st.session_state.query_history.append(user_query)

            # Clear input (use del to avoid widget modification error)
            if 'main_query_input' in st.session_state:
                del st.session_state.main_query_input

            execution_time = time.time() - start_time
            st.success(f"✅ Query processed in {execution_time:.2f}s")

    except Exception as e:
        execution_time = time.time() - start_time
        st.session_state.query_stats['failed_queries'] += 1

        error_msg = f"Error processing query: {str(e)}"
        st.error(error_msg)
        logger.error(f"Query execution error: {traceback.format_exc()}")

        # Add error to chat history
        st.session_state.chat_history.append((
            user_query,
            f"❌ {error_msg}",
            None
        ))

def process_intelligent_query(user_query: str) -> Dict[str, Any]:
    """Process query using intelligent query engine with enhanced formatting and retry logic"""
    try:
        mongodb_manager = st.session_state.mongodb_manager
        viz_engine = st.session_state.viz_engine

        if not mongodb_manager.is_connected:
            return {
                'success': False,
                'response': "❌ **Not connected to MongoDB**\n\nPlease connect to your database first using the sidebar.",
                'visualization': None,
                'execution_time': 0
            }

        # Try to execute query with retry logic
        query_analysis = execute_query_with_retry(user_query, mongodb_manager)

        # Format the response like ChatGPT/Claude
        response_text = format_intelligent_response(user_query, query_analysis, mongodb_manager)

        # Only create visualization if explicitly requested
        visualization = None
        if should_create_visualization(user_query) and query_analysis.get('visualization_data'):
            try:
                visualization = viz_engine.create_visualization(
                    query_analysis['visualization_data'],
                    user_query
                )
                logger.info("Visualization created successfully")
            except Exception as e:
                logger.error(f"Visualization creation failed: {e}")

        return {
            'success': True,
            'response': response_text,
            'visualization': visualization,
            'execution_time': query_analysis.get('execution_time', 0.5),
            'connected': True,
            'query_details': query_analysis
        }

    except Exception as e:
        logger.error(f"Intelligent query processing failed: {e}")
        return {
            'success': False,
            'response': f"I apologize, but I encountered an unexpected error while processing your question.\n\n**Error Details:** {str(e)}\n\n💡 **What you can try:**\n• Rephrase your question in simpler terms\n• Check your database connection\n• Try asking about a different collection\n• Contact support if the issue persists",
            'visualization': None,
            'execution_time': 0
        }

def execute_query_with_retry(user_query: str, mongodb_manager, max_retries: int = 2) -> Dict[str, Any]:
    """Execute query with intelligent retry logic"""

    last_error = None
    retry_count = 0

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                logger.info(f"Retry attempt {attempt} for query: {user_query}")
                # Modify query strategy based on previous error
                modified_query = modify_query_for_retry(user_query, last_error, attempt)
                query_analysis = analyze_and_execute_query(modified_query, mongodb_manager)

                # Add retry information to the response
                query_analysis['retry_info'] = {
                    'attempt': attempt,
                    'original_query': user_query,
                    'modified_query': modified_query,
                    'previous_error': str(last_error)
                }
            else:
                query_analysis = analyze_and_execute_query(user_query, mongodb_manager)

            # If successful, return the result
            if query_analysis['result']['success']:
                if attempt > 0:
                    logger.info(f"Query succeeded on retry attempt {attempt}")
                return query_analysis
            else:
                last_error = query_analysis['result'].get('error', 'Unknown error')

        except Exception as e:
            last_error = str(e)
            logger.error(f"Query attempt {attempt + 1} failed: {e}")

        retry_count = attempt

    # If all retries failed, return the last attempt with retry information
    query_analysis['result']['retry_attempts'] = retry_count + 1
    query_analysis['result']['final_error'] = last_error

    return query_analysis

def modify_query_for_retry(original_query: str, error: str, attempt: int) -> str:
    """Modify query based on previous error for retry"""

    query_lower = original_query.lower()
    error_lower = str(error).lower() if error else ""

    # Strategy 1: If price field not found, try generic data query
    if attempt == 1 and ("price" in error_lower or "field" in error_lower):
        if "expensive" in query_lower or "costly" in query_lower:
            return "show me some products with their details"
        elif "cheap" in query_lower:
            return "show me some products with their details"
        else:
            return "show me some data from this collection"

    # Strategy 2: If collection not found, try more generic approach
    elif attempt == 2 and ("not found" in error_lower or "collection" in error_lower):
        return "list all available collections"

    # Strategy 3: Fallback to very simple query
    else:
        return "show me some data"

def should_create_visualization(user_query: str) -> bool:
    """Determine if visualization should be created based on user query"""
    viz_keywords = [
        'chart', 'graph', 'plot', 'visualize', 'visualization', 'show chart',
        'create graph', 'plot data', 'bar chart', 'pie chart', 'line graph',
        'histogram', 'scatter plot', 'dashboard'
    ]

    query_lower = user_query.lower()
    return any(keyword in query_lower for keyword in viz_keywords)

def analyze_and_execute_query(user_query: str, mongodb_manager) -> Dict[str, Any]:
    """Analyze user query and execute appropriate MongoDB operations"""
    import time
    start_time = time.time()

    query_lower = user_query.lower()
    collections = mongodb_manager.get_collections()

    # Find the most relevant collection
    target_collection = find_relevant_collection(query_lower, collections)
    confidence_score = calculate_query_confidence(query_lower, target_collection, collections)

    # Determine query intent
    intent = determine_query_intent(query_lower)

    # Generate MongoDB query
    mongodb_query = generate_mongodb_query(intent, target_collection)

    # Execute the query
    query_result = execute_mongodb_query_with_details(
        intent, target_collection, mongodb_query, mongodb_manager
    )

    execution_time = time.time() - start_time

    return {
        'user_query': user_query,
        'intent': intent,
        'target_collection': target_collection,
        'confidence_score': confidence_score,
        'mongodb_query': mongodb_query,
        'result': query_result,
        'execution_time': execution_time,
        'available_collections': collections,
        'visualization_data': query_result.get('visualization_data')
    }

def format_intelligent_response(user_query: str, analysis: Dict[str, Any], mongodb_manager) -> str:
    """Format response in natural ChatGPT/Claude conversational style"""

    result = analysis['result']

    # Log performance metrics to terminal (not shown in UI)
    logger.info(f"Query executed in {analysis['execution_time']:.3f}s, processed {result.get('documents_processed', 0)} documents")

    # Handle retry information if present
    retry_info = analysis.get('retry_info')
    if retry_info:
        logger.info(f"Query succeeded on retry attempt {retry_info['attempt']} after error: {retry_info['previous_error']}")

    if not result['success']:
        return format_error_response_with_retry(user_query, result, analysis)

    # Start with a natural conversational response
    response = ""

    # Add retry context if this was a retry attempt
    if retry_info:
        response += f"*I had to adjust my approach to answer your question, but I found the information you're looking for!*\n\n"

    if result['type'] == 'single_document':
        response += format_conversational_single_result(user_query, result, analysis)
    elif result['type'] == 'multiple_documents':
        response += format_conversational_multiple_results(user_query, result, analysis)
    elif result['type'] == 'count':
        response += format_conversational_count_result(user_query, result, analysis)
    elif result['type'] == 'analysis':
        response += format_conversational_analysis_result(user_query, result, analysis)
    else:
        response += f"I found some information in your `{analysis['target_collection']}` collection. Here's what I discovered:\n\n"
        response += result.get('formatted_result', 'Query executed successfully.')

    # Add MongoDB query details in a collapsible format
    response += f"\n\n<details>\n<summary>🔍 Technical Details</summary>\n\n"
    response += f"**Query Analysis:**\n"
    response += f"• Intent: {analysis['intent']['type'].title()} Query\n"
    response += f"• Target Collection: `{analysis['target_collection']}`\n"
    response += f"• Confidence: {analysis['confidence_score']:.1f}%\n"
    response += f"• Database: `{mongodb_manager.current_database}`\n"

    # Add retry information if applicable
    if retry_info:
        response += f"• Retry Attempt: {retry_info['attempt']} (succeeded after initial failure)\n"
        response += f"• Original Query: \"{retry_info['original_query']}\"\n"
        response += f"• Modified Query: \"{retry_info['modified_query']}\"\n"

    response += f"\n**MongoDB Query:**\n```javascript\n{analysis['mongodb_query']}\n```\n</details>"

    # Add helpful suggestions
    if should_show_suggestions(user_query):
        suggestions = generate_query_suggestions(analysis['target_collection'], analysis['available_collections'])
        if suggestions:
            response += f"\n\n💡 **You might also want to ask:**\n"
            for suggestion in suggestions[:3]:
                response += f"• {suggestion}\n"

    return response

def format_error_response_with_retry(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format error response with retry information"""
    error_msg = result.get('error', 'Unknown error occurred')
    retry_attempts = result.get('retry_attempts', 1)
    final_error = result.get('final_error', error_msg)

    if retry_attempts > 1:
        response = f"I tried {retry_attempts} different approaches to answer your question \"{user_query}\", but unfortunately couldn't find the information you're looking for.\n\n"
        response += f"**Final Error:** {final_error}\n\n"
        response += "**What I tried:**\n"
        response += "• First, I looked for the exact information you requested\n"
        response += "• Then, I tried a more general approach to find related data\n"
        response += "• Finally, I attempted to explore the collection structure\n\n"
    else:
        response = f"I encountered an issue while processing your question: \"{user_query}\"\n\n"
        response += f"**Error:** {error_msg}\n\n"

    # Provide helpful suggestions based on the error
    if "not found" in final_error.lower():
        response += "💡 **Suggestions:**\n"
        response += f"• The `{analysis['target_collection']}` collection might not exist\n"
        response += "• Try asking 'list all collections' to see what's available\n"
        response += "• Check if you're connected to the right database\n"
    elif "price" in final_error.lower():
        response += "💡 **Suggestions:**\n"
        response += "• This collection might not have price information\n"
        response += "• Try asking 'show me some data' to see available fields\n"
        response += "• Ask about a different collection that might have pricing data\n"
    else:
        response += "💡 **What you can try:**\n"
        response += "• Ask me to 'show some data' to explore the collection\n"
        response += "• Try a simpler question like 'count documents'\n"
        response += "• Check your database connection\n"
        response += "• Rephrase your question in different words\n"

    return response

def format_conversational_single_result(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format single document result in conversational style"""
    doc = result['document']
    intent = analysis['intent']['operation']

    response = ""

    if intent == 'max_price':
        price_field = result.get('price_field', 'price')
        price_value = doc.get(price_field, 0)
        item_name = doc.get('name', doc.get('title', doc.get('product_name', 'item')))

        response += f"The most expensive product I found is **{item_name}** priced at **${price_value:,.2f}**.\n\n"

        # Add additional details naturally
        details = []
        if 'category' in doc:
            details.append(f"Category: {doc['category']}")
        if 'brand' in doc:
            details.append(f"Brand: {doc['brand']}")
        if 'description' in doc:
            desc = doc['description'][:150] + "..." if len(doc['description']) > 150 else doc['description']
            details.append(f"Description: {desc}")

        if details:
            response += "**Additional Details:**\n"
            for detail in details:
                response += f"• {detail}\n"

    elif intent == 'min_price':
        price_field = result.get('price_field', 'price')
        price_value = doc.get(price_field, 0)
        item_name = doc.get('name', doc.get('title', doc.get('product_name', 'item')))

        response += f"The cheapest product I found is **{item_name}** priced at **${price_value:,.2f}**.\n\n"

        # Add additional details
        if 'category' in doc:
            response += f"This item is in the **{doc['category']}** category"
            if 'brand' in doc:
                response += f" from **{doc['brand']}**"
            response += ".\n\n"

        if 'description' in doc:
            response += f"**Description:** {doc['description']}\n"

    else:
        # General single document response
        item_name = doc.get('name', doc.get('title', doc.get('product_name', 'Document')))
        response += f"I found information about **{item_name}**:\n\n"

        # Show key fields naturally
        key_fields = ['name', 'title', 'product_name', 'price', 'product_price', 'category', 'brand', 'description']
        shown_fields = set()

        for field in key_fields:
            if field in doc and field not in shown_fields and field != '_id':
                value = doc[field]
                if field in ['price', 'product_price']:
                    response += f"• **Price:** ${value:,.2f}\n"
                elif field == 'description':
                    desc = value[:200] + "..." if len(str(value)) > 200 else value
                    response += f"• **Description:** {desc}\n"
                else:
                    response += f"• **{field.replace('_', ' ').title()}:** {value}\n"
                shown_fields.add(field)

    return response

def format_conversational_multiple_results(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format multiple documents result in conversational style"""
    docs = result['documents']
    total = result.get('total_available', len(docs))
    collection = analysis['target_collection']

    if not docs:
        return f"I didn't find any documents in the `{collection}` collection. The collection might be empty or there might be a connection issue."

    # Natural introduction
    if len(docs) == total:
        response = f"I found all **{total}** items in your `{collection}` collection:\n\n"
    else:
        response = f"Here are **{len(docs)}** items from your `{collection}` collection (total: {total}):\n\n"

    # Show all documents without truncation
    for i, doc in enumerate(docs, 1):
        item_name = doc.get('name', doc.get('title', doc.get('product_name', f'Item {i}')))
        response += f"**{i}. {item_name}**\n"

        # Show key information in a natural way
        details = []

        if 'price' in doc:
            details.append(f"💰 ${doc['price']:,.2f}")
        elif 'product_price' in doc:
            details.append(f"💰 ${doc['product_price']:,.2f}")

        if 'category' in doc:
            details.append(f"📂 {doc['category']}")

        if 'brand' in doc:
            details.append(f"🏷️ {doc['brand']}")

        if details:
            response += f"   {' • '.join(details)}\n"

        if 'description' in doc:
            desc = doc['description']
            if len(desc) > 100:
                desc = desc[:100] + "..."
            response += f"   📝 {desc}\n"

        response += "\n"

    # Show remaining count naturally if there are more
    if total > len(docs):
        remaining = total - len(docs)
        response += f"*There are {remaining} more items in this collection. Ask me to show more if you'd like to see them!*\n"

    return response

def format_error_response(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format error response in conversational style"""
    error_msg = result.get('error', 'Unknown error occurred')

    response = f"I encountered an issue while processing your question: \"{user_query}\"\n\n"
    response += f"**Error:** {error_msg}\n\n"

    # Provide helpful suggestions based on the error
    if "not found" in error_msg.lower():
        response += "💡 **Suggestions:**\n"
        response += f"• Check if the `{analysis['target_collection']}` collection exists\n"
        response += "• Try asking about a different collection\n"
        response += "• Use 'list collections' to see available collections\n"
    elif "price" in error_msg.lower():
        response += "💡 **Suggestions:**\n"
        response += "• This collection might not have price information\n"
        response += "• Try asking 'show me some data' to see what fields are available\n"
        response += "• Ask about a different collection that might have pricing data\n"
    else:
        response += "💡 **What you can try:**\n"
        response += "• Ask me to 'show some data' to explore the collection\n"
        response += "• Try a simpler question like 'count documents'\n"
        response += "• Check your database connection\n"

    return response

def format_conversational_count_result(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format count result in conversational style"""
    count = result['count']
    collection = analysis['target_collection']

    if count == 0:
        return f"The `{collection}` collection is currently empty. There are no documents to show."
    elif count == 1:
        return f"There is **1 document** in the `{collection}` collection."
    else:
        response = f"There are **{count:,} documents** in the `{collection}` collection.\n\n"

        # Add context based on collection size
        if count > 10000:
            response += "That's quite a large collection! You might want to ask more specific questions to get targeted results."
        elif count > 1000:
            response += "This is a substantial collection with plenty of data to explore."
        elif count > 100:
            response += "This collection has a good amount of data to work with."
        else:
            response += "This is a relatively small collection - perfect for exploring all the data."

        return response

def format_conversational_analysis_result(user_query: str, result: Dict[str, Any], analysis: Dict[str, Any]) -> str:
    """Format analysis result in conversational style"""
    sample_doc = result.get('sample_document')
    total_docs = result.get('total_documents', 0)
    fields = result.get('fields', [])
    collection = analysis['target_collection']

    response = f"Let me tell you about the `{collection}` collection:\n\n"
    response += f"📊 **Collection Overview:**\n"
    response += f"• Total documents: {total_docs:,}\n"
    response += f"• Available fields: {len(fields)}\n\n"

    if fields:
        response += "🏷️ **Available Fields:**\n"
        # Group fields by type for better presentation
        important_fields = []
        other_fields = []

        for field in fields:
            if field in ['name', 'title', 'price', 'description', 'category', 'brand']:
                important_fields.append(field)
            elif field != '_id':
                other_fields.append(field)

        if important_fields:
            response += f"• **Key fields:** {', '.join(important_fields)}\n"
        if other_fields:
            response += f"• **Other fields:** {', '.join(other_fields[:10])}"
            if len(other_fields) > 10:
                response += f" and {len(other_fields) - 10} more"
            response += "\n"

    if sample_doc:
        response += f"\n📝 **Sample Document Structure:**\n"
        for field, value in list(sample_doc.items())[:5]:
            if field != '_id':
                value_preview = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                response += f"• `{field}`: {value_preview}\n"

    return response

def should_show_suggestions(user_query: str) -> bool:
    """Determine if suggestions should be shown based on query"""
    # Don't show suggestions for very specific queries
    specific_indicators = ['specific', 'exact', 'just', 'only', 'particular']
    return not any(indicator in user_query.lower() for indicator in specific_indicators)

def determine_query_intent(query_lower: str) -> Dict[str, Any]:
    """Determine the intent of the user query"""

    intents = {
        'find_expensive': {
            'keywords': ['expensive', 'costlier', 'highest price', 'most expensive', 'maximum price', 'priciest'],
            'type': 'aggregation',
            'operation': 'max_price'
        },
        'find_cheap': {
            'keywords': ['cheap', 'cheapest', 'lowest price', 'minimum price', 'least expensive'],
            'type': 'aggregation',
            'operation': 'min_price'
        },
        'count': {
            'keywords': ['count', 'how many', 'total', 'number of'],
            'type': 'count',
            'operation': 'count_documents'
        },
        'list_all': {
            'keywords': ['show', 'list', 'all', 'get', 'find', 'display'],
            'type': 'find',
            'operation': 'find_multiple'
        },
        'describe': {
            'keywords': ['describe', 'what is', 'tell me about', 'information'],
            'type': 'analysis',
            'operation': 'describe_collection'
        }
    }

    # Score each intent
    best_intent = None
    best_score = 0

    for intent_name, intent_data in intents.items():
        score = sum(1 for keyword in intent_data['keywords'] if keyword in query_lower)
        if score > best_score:
            best_score = score
            best_intent = intent_data
            best_intent['name'] = intent_name

    # Default intent
    if not best_intent:
        best_intent = {
            'name': 'general_query',
            'type': 'find',
            'operation': 'find_multiple',
            'keywords': []
        }

    return best_intent

def generate_mongodb_query(intent: Dict[str, Any], collection: str) -> str:
    """Generate MongoDB query string based on intent"""

    if intent['operation'] == 'max_price':
        return f"db.{collection}.find().sort({{price: -1}}).limit(1)"
    elif intent['operation'] == 'min_price':
        return f"db.{collection}.find({{price: {{$gt: 0}}}}).sort({{price: 1}}).limit(1)"
    elif intent['operation'] == 'count_documents':
        return f"db.{collection}.countDocuments({{}})"
    elif intent['operation'] == 'find_multiple':
        return f"db.{collection}.find({{}}).limit(5)"
    elif intent['operation'] == 'describe_collection':
        return f"db.{collection}.findOne({{}})"
    else:
        return f"db.{collection}.find({{}}).limit(3)"

def calculate_query_confidence(query_lower: str, target_collection: str, collections: List[str]) -> float:
    """Calculate confidence score for the query analysis"""

    score = 50.0  # Base score

    # Collection name match
    collection_lower = target_collection.lower()
    query_words = query_lower.split()

    for word in query_words:
        if word in collection_lower:
            score += 20.0

    # Intent clarity
    clear_intents = ['expensive', 'cheap', 'count', 'show', 'list', 'price']
    if any(intent in query_lower for intent in clear_intents):
        score += 15.0

    # Specific field mentions
    common_fields = ['price', 'name', 'title', 'description', 'category']
    if any(field in query_lower for field in common_fields):
        score += 10.0

    return min(score, 95.0)  # Cap at 95%

def execute_mongodb_query_with_details(intent: Dict[str, Any], collection: str,
                                     mongodb_query: str, mongodb_manager) -> Dict[str, Any]:
    """Execute MongoDB query and return detailed results"""

    try:
        operation = intent['operation']

        # Get database and collection references
        if not mongodb_manager.is_connected or not mongodb_manager.client:
            return {
                'success': False,
                'error': "Not connected to MongoDB",
                'type': 'error',
                'documents_processed': 0
            }

        db = mongodb_manager.client[mongodb_manager.current_database]
        coll = db[collection]

        if operation == 'count_documents':
            count = coll.count_documents({})
            return {
                'success': True,
                'type': 'count',
                'count': count,
                'documents_processed': count,
                'data': None,
                'visualization_data': None
            }

        elif operation in ['max_price', 'min_price']:
            # Find documents with price field
            sort_order = -1 if operation == 'max_price' else 1

            # Try different price field names
            price_fields = ['price', 'product_price', 'cost', 'amount', 'value']
            result_doc = None

            for price_field in price_fields:
                try:
                    if operation == 'min_price':
                        # For minimum, exclude zero/null prices
                        query = {price_field: {"$gt": 0}}
                    else:
                        query = {}

                    docs = list(coll.find(query).sort(price_field, sort_order).limit(1))
                    if docs and price_field in docs[0]:
                        result_doc = docs[0]
                        result_doc['_price_field'] = price_field
                        break
                except:
                    continue

            if result_doc:
                return {
                    'success': True,
                    'type': 'single_document',
                    'document': result_doc,
                    'documents_processed': 1,
                    'price_field': result_doc.get('_price_field'),
                    'visualization_data': None
                }
            else:
                return {
                    'success': False,
                    'error': f"No documents with price information found in {collection}",
                    'type': 'error'
                }

        elif operation == 'find_multiple':
            docs = list(coll.find({}).limit(5))
            return {
                'success': True,
                'type': 'multiple_documents',
                'documents': docs,
                'documents_processed': len(docs),
                'total_available': coll.count_documents({}),
                'visualization_data': docs if len(docs) > 0 else None
            }

        elif operation == 'describe_collection':
            # Get sample document and collection stats
            sample_doc = coll.find_one({})
            count = coll.count_documents({})

            return {
                'success': True,
                'type': 'analysis',
                'sample_document': sample_doc,
                'total_documents': count,
                'documents_processed': 1,
                'fields': list(sample_doc.keys()) if sample_doc else [],
                'visualization_data': None
            }

        else:
            # Default find operation
            docs = list(coll.find({}).limit(3))
            return {
                'success': True,
                'type': 'multiple_documents',
                'documents': docs,
                'documents_processed': len(docs),
                'visualization_data': docs if len(docs) > 0 else None
            }

    except Exception as e:
        logger.error(f"Query execution failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'type': 'error',
            'documents_processed': 0
        }

def format_single_document_result(result: Dict[str, Any]) -> str:
    """Format single document result for display"""
    doc = result['document']
    price_field = result.get('price_field', 'price')

    response = ""

    # Show the main result
    if price_field in doc:
        price_value = doc[price_field]
        response += f"**Found:** {doc.get('name', doc.get('title', doc.get('product_name', 'Item')))}\n"
        response += f"**Price:** ${price_value:,.2f}\n\n"

    # Show key fields
    response += "**Document Details:**\n"
    key_fields = ['name', 'title', 'product_name', 'description', 'category', 'brand']

    for field in key_fields:
        if field in doc and field != '_id':
            value = doc[field]
            if isinstance(value, str) and len(value) > 100:
                value = value[:100] + "..."
            response += f"• **{field.title()}:** {value}\n"

    # Show price field if different name
    if price_field != 'price' and price_field in doc:
        response += f"• **{price_field.title()}:** ${doc[price_field]:,.2f}\n"

    return response + "\n"

def format_multiple_documents_result(result: Dict[str, Any]) -> str:
    """Format multiple documents result for display"""
    docs = result['documents']
    total = result.get('total_available', len(docs))

    response = f"**Found {len(docs)} documents** (showing first {len(docs)} of {total} total)\n\n"

    for i, doc in enumerate(docs, 1):
        response += f"**{i}. {doc.get('name', doc.get('title', doc.get('product_name', f'Document {i}')))}**\n"

        # Show key information
        if 'price' in doc:
            response += f"   💰 Price: ${doc['price']:,.2f}\n"
        elif 'product_price' in doc:
            response += f"   💰 Price: ${doc['product_price']:,.2f}\n"

        if 'category' in doc:
            response += f"   📂 Category: {doc['category']}\n"

        if 'description' in doc:
            desc = doc['description']
            if len(desc) > 80:
                desc = desc[:80] + "..."
            response += f"   📝 Description: {desc}\n"

        response += "\n"

    if total > len(docs):
        response += f"*... and {total - len(docs)} more documents*\n"

    return response

def format_aggregation_result(result: Dict[str, Any]) -> str:
    """Format aggregation result for display"""
    return format_single_document_result(result)

def format_count_result(result: Dict[str, Any]) -> str:
    """Format count result for display"""
    count = result['count']

    response = f"**Total Documents:** {count:,}\n\n"

    if count > 0:
        response += "📊 **Collection Statistics:**\n"
        response += f"• Total records: {count:,}\n"

        if count > 1000:
            response += "• This is a large collection with substantial data\n"
        elif count > 100:
            response += "• This is a medium-sized collection\n"
        else:
            response += "• This is a small collection\n"
    else:
        response += "📭 This collection is empty.\n"

    return response + "\n"

def generate_query_suggestions(target_collection: str, all_collections: List[str]) -> List[str]:
    """Generate helpful query suggestions"""

    suggestions = []

    # Collection-specific suggestions
    if 'product' in target_collection.lower():
        suggestions.extend([
            f"What is the most expensive product in {target_collection}?",
            f"Show me all products from {target_collection}",
            f"Count total products in {target_collection}"
        ])
    elif 'user' in target_collection.lower():
        suggestions.extend([
            f"How many users are in {target_collection}?",
            f"Show me some users from {target_collection}",
            f"What information do we have about users?"
        ])
    elif 'order' in target_collection.lower():
        suggestions.extend([
            f"What is the highest order value in {target_collection}?",
            f"Show me recent orders from {target_collection}",
            f"Count total orders in {target_collection}"
        ])
    else:
        suggestions.extend([
            f"Show me some data from {target_collection}",
            f"How many documents are in {target_collection}?",
            f"What fields are available in {target_collection}?"
        ])

    # Add general suggestions
    if len(all_collections) > 1:
        other_collections = [c for c in all_collections if c != target_collection][:2]
        for collection in other_collections:
            suggestions.append(f"Switch to {collection} collection and show me some data")

    return suggestions[:5]

def execute_simple_mongodb_query(user_query: str, mongodb_manager) -> str:
    """Execute a simple MongoDB query based on user intent"""
    try:
        query_lower = user_query.lower()
        collections = mongodb_manager.get_collections()

        if not collections:
            return "No collections found in the current database."

        db = mongodb_manager.client[mongodb_manager.current_database]

        # Find the most relevant collection based on query
        target_collection = find_relevant_collection(query_lower, collections)
        collection = db[target_collection]

        # Enhanced intent-based queries
        if any(word in query_lower for word in ['expensive', 'costlier', 'highest price', 'most expensive', 'maximum price']):
            # Find most expensive product
            return find_most_expensive_item(collection, target_collection)

        elif any(word in query_lower for word in ['cheapest', 'lowest price', 'minimum price', 'least expensive']):
            # Find cheapest product
            return find_cheapest_item(collection, target_collection)

        elif any(word in query_lower for word in ['count', 'how many', 'total']):
            # Count documents
            count = collection.count_documents({})
            return f"Found **{count}** documents in collection '{target_collection}'"

        elif any(word in query_lower for word in ['show', 'list', 'find', 'get', 'all']):
            # Show sample documents with actual data
            return show_sample_documents(collection, target_collection)

        elif any(word in query_lower for word in ['collections', 'tables']):
            # List collections with document counts
            collection_info = []
            for coll_name in collections[:5]:  # Limit to first 5
                try:
                    count = db[coll_name].count_documents({})
                    collection_info.append(f"**{coll_name}** ({count} docs)")
                except:
                    collection_info.append(f"**{coll_name}**")
            return f"Available collections: {', '.join(collection_info)}"

        else:
            # Default: intelligent analysis based on collection content
            return analyze_collection_content(collection, target_collection, query_lower)

    except Exception as e:
        logger.error(f"Simple query execution failed: {e}")
        return f"Query execution failed: {str(e)}"

def find_relevant_collection(query_lower: str, collections: List[str]) -> str:
    """Find the most relevant collection based on query keywords"""
    # Keywords to collection mapping
    collection_keywords = {
        'product': ['product', 'item', 'goods', 'merchandise'],
        'user': ['user', 'customer', 'person', 'account'],
        'order': ['order', 'purchase', 'transaction', 'sale'],
        'review': ['review', 'comment', 'feedback', 'rating'],
        'character': ['character', 'hero', 'villain', 'person'],
        'comic': ['comic', 'book', 'story', 'issue']
    }

    # Score each collection
    scores = {}
    for collection in collections:
        score = 0
        collection_lower = collection.lower()

        # Direct name match
        if any(keyword in collection_lower for keyword in query_lower.split()):
            score += 10

        # Keyword matching
        for coll_type, keywords in collection_keywords.items():
            if coll_type in collection_lower:
                if any(keyword in query_lower for keyword in keywords):
                    score += 5

        scores[collection] = score

    # Return highest scoring collection, or first one if no matches
    if scores:
        best_collection = max(scores, key=scores.get)
        if scores[best_collection] > 0:
            return best_collection

    return collections[0]

def find_most_expensive_item(collection, collection_name: str) -> str:
    """Find the most expensive item in a collection"""
    try:
        # Common price field names
        price_fields = ['price', 'cost', 'amount', 'value', 'retail_price', 'sale_price']

        # Try to find a price field
        sample_doc = collection.find_one()
        if not sample_doc:
            return f"Collection '{collection_name}' is empty"

        price_field = None
        for field in price_fields:
            if field in sample_doc:
                price_field = field
                break

        if not price_field:
            # Look for any numeric field
            for field, value in sample_doc.items():
                if isinstance(value, (int, float)) and field != '_id':
                    price_field = field
                    break

        if price_field:
            # Find document with highest price
            most_expensive = collection.find().sort(price_field, -1).limit(1)
            doc = list(most_expensive)[0] if most_expensive else None

            if doc:
                price = doc.get(price_field, 'Unknown')
                name = doc.get('name', doc.get('title', doc.get('product_name', 'Unknown Item')))

                result = f"🏆 **Most expensive item in '{collection_name}':**\n"
                result += f"📦 **Name:** {name}\n"
                result += f"💰 **{price_field.title()}:** {price}\n"

                # Add other relevant fields
                other_fields = ['description', 'category', 'brand', 'type']
                for field in other_fields:
                    if field in doc and doc[field]:
                        result += f"📋 **{field.title()}:** {str(doc[field])[:50]}...\n"

                return result
            else:
                return f"No documents found in '{collection_name}'"
        else:
            return f"No price field found in '{collection_name}'. Available fields: {list(sample_doc.keys())[:5]}"

    except Exception as e:
        logger.error(f"Error finding most expensive item: {e}")
        return f"Error finding most expensive item: {str(e)}"

def find_cheapest_item(collection, collection_name: str) -> str:
    """Find the cheapest item in a collection"""
    try:
        # Similar to most expensive but sort ascending
        price_fields = ['price', 'cost', 'amount', 'value', 'retail_price', 'sale_price']

        sample_doc = collection.find_one()
        if not sample_doc:
            return f"Collection '{collection_name}' is empty"

        price_field = None
        for field in price_fields:
            if field in sample_doc:
                price_field = field
                break

        if not price_field:
            for field, value in sample_doc.items():
                if isinstance(value, (int, float)) and field != '_id':
                    price_field = field
                    break

        if price_field:
            cheapest = collection.find({price_field: {"$gt": 0}}).sort(price_field, 1).limit(1)
            doc = list(cheapest)[0] if cheapest else None

            if doc:
                price = doc.get(price_field, 'Unknown')
                name = doc.get('name', doc.get('title', doc.get('product_name', 'Unknown Item')))

                result = f"💰 **Cheapest item in '{collection_name}':**\n"
                result += f"📦 **Name:** {name}\n"
                result += f"💵 **{price_field.title()}:** {price}\n"

                return result
            else:
                return f"No documents with valid prices found in '{collection_name}'"
        else:
            return f"No price field found in '{collection_name}'"

    except Exception as e:
        logger.error(f"Error finding cheapest item: {e}")
        return f"Error finding cheapest item: {str(e)}"

def show_sample_documents(collection, collection_name: str) -> str:
    """Show sample documents from a collection"""
    try:
        docs = list(collection.find().limit(3))
        if not docs:
            return f"Collection '{collection_name}' is empty"

        result = f"📄 **Sample documents from '{collection_name}':**\n\n"

        for i, doc in enumerate(docs, 1):
            result += f"**Document {i}:**\n"

            # Show key fields
            key_fields = ['name', 'title', 'product_name', 'description', 'price', 'cost', 'category', 'type']
            shown_fields = 0

            for field in key_fields:
                if field in doc and shown_fields < 4:
                    value = str(doc[field])[:50]
                    if len(str(doc[field])) > 50:
                        value += "..."
                    result += f"  • **{field.title()}:** {value}\n"
                    shown_fields += 1

            if shown_fields == 0:
                # Show first few fields if no key fields found
                for field, value in list(doc.items())[:3]:
                    if field != '_id':
                        value_str = str(value)[:30]
                        if len(str(value)) > 30:
                            value_str += "..."
                        result += f"  • **{field}:** {value_str}\n"

            result += "\n"

        return result

    except Exception as e:
        logger.error(f"Error showing sample documents: {e}")
        return f"Error showing sample documents: {str(e)}"

def analyze_collection_content(collection, collection_name: str, query: str) -> str:
    """Analyze collection content and provide relevant insights"""
    try:
        count = collection.count_documents({})
        sample_doc = collection.find_one()

        if not sample_doc:
            return f"Collection '{collection_name}' is empty"

        result = f"📊 **Analysis of '{collection_name}' ({count} documents):**\n\n"

        # Analyze fields
        fields = list(sample_doc.keys())
        result += f"📋 **Available fields:** {', '.join([f for f in fields[:8] if f != '_id'])}\n"

        # Look for interesting patterns
        numeric_fields = []
        text_fields = []

        for field, value in sample_doc.items():
            if field != '_id':
                if isinstance(value, (int, float)):
                    numeric_fields.append(field)
                elif isinstance(value, str):
                    text_fields.append(field)

        if numeric_fields:
            result += f"🔢 **Numeric fields:** {', '.join(numeric_fields[:5])}\n"

        if text_fields:
            result += f"📝 **Text fields:** {', '.join(text_fields[:5])}\n"

        # Suggest queries
        result += f"\n💡 **Try asking:**\n"
        if 'price' in fields or 'cost' in fields:
            result += f"  • 'What is the most expensive item?'\n"
            result += f"  • 'Show me the cheapest products'\n"
        result += f"  • 'Show me some {collection_name}'\n"
        result += f"  • 'Count all {collection_name}'\n"

        return result

    except Exception as e:
        logger.error(f"Error analyzing collection: {e}")
        return f"Error analyzing collection: {str(e)}"

def get_sample_data_for_visualization(mongodb_manager) -> List[Dict[str, Any]]:
    """Get sample data from MongoDB for visualization"""
    try:
        collections = mongodb_manager.get_collections()
        if not collections:
            return None

        db = mongodb_manager.client[mongodb_manager.current_database]
        first_collection = collections[0]
        collection = db[first_collection]

        # Get a few sample documents
        docs = list(collection.find().limit(10))
        if not docs:
            return None

        # Try to find numeric fields for visualization
        sample_doc = docs[0]
        numeric_fields = []
        categorical_fields = []

        for field, value in sample_doc.items():
            if isinstance(value, (int, float)) and field != '_id':
                numeric_fields.append(field)
            elif isinstance(value, str) and field != '_id':
                categorical_fields.append(field)

        # Create visualization data
        viz_data = []
        for doc in docs:
            viz_item = {}

            # Add a categorical field
            if categorical_fields:
                cat_field = categorical_fields[0]
                viz_item['category'] = str(doc.get(cat_field, 'Unknown'))[:20]  # Truncate long strings
            else:
                viz_item['category'] = f"Item {len(viz_data) + 1}"

            # Add a numeric field
            if numeric_fields:
                num_field = numeric_fields[0]
                viz_item['value'] = doc.get(num_field, 0)
            else:
                viz_item['value'] = len(viz_data) + 1

            viz_data.append(viz_item)

        return viz_data if viz_data else None

    except Exception as e:
        logger.error(f"Sample data extraction failed: {e}")
        return None

def process_demo_query(user_query: str) -> Dict[str, Any]:
    """Process query in demo mode with mock data"""
    try:
        # Simulate processing time
        time.sleep(0.5)

        # Generate mock response based on query content
        query_lower = user_query.lower()

        if 'chart' in query_lower or 'visualization' in query_lower:
            response = f"🎨 I understand you want to create a visualization for: '{user_query}'. In demo mode, I would analyze your data and create an appropriate chart!"
        elif 'count' in query_lower:
            response = f"📊 I would count the relevant records for: '{user_query}'. Demo result: Found 42 matching records."
        elif 'top' in query_lower or 'best' in query_lower:
            response = f"🏆 I would find the top results for: '{user_query}'. Demo shows top performers in your dataset!"
        elif 'show' in query_lower or 'list' in query_lower:
            response = f"📋 I would display the data for: '{user_query}'. Demo mode shows sample records from your database."
        else:
            response = f"🤖 I understand your query: '{user_query}'. In a real database connection, I would analyze your data and provide specific results!"

        response += "\n\n💡 **Connect to your MongoDB database to see real results!**"

        return {
            'success': True,
            'response': response,
            'visualization': None,
            'execution_time': 0.5,
            'demo_mode': True
        }

    except Exception as e:
        return {
            'success': False,
            'response': f"❌ Demo query processing failed: {str(e)}",
            'visualization': None,
            'execution_time': 0
        }

if __name__ == "__main__":
    main()