"""
DataPilot - Intelligent MongoDB Query System
A sophisticated system for natural language querying of MongoDB databases
with intelligent collection discovery and visualization capabilities.
"""

import streamlit as st
import os
import sys
import asyncio
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# Core imports
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List, Optional
import time
import traceback
import random

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our custom modules
try:
    from core.mongodb_manager import MongoDBManager
    from core.query_engine import IntelligentQueryEngine
    from core.visualization_engine import VisualizationEngine
    from core.schema_discovery import SchemaDiscoveryEngine
    from ui.components import UIComponents
    from utils.config import config
    MODULES_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import modules: {e}")
    MODULES_AVAILABLE = False
    st.error("⚠️ Some modules are not available. Please check dependencies.")

def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'initialized' not in st.session_state:
        st.session_state.initialized = True
        st.session_state.connected = False
        st.session_state.demo_mode = False
        st.session_state.current_database = None
        st.session_state.chat_history = []
        st.session_state.query_history = []
        st.session_state.discovered_schemas = {}
        st.session_state.connection_config = {}
        st.session_state.query_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'visualizations_created': 0
        }

        # Initialize core components
        if MODULES_AVAILABLE:
            try:
                st.session_state.mongodb_manager = MongoDBManager()
                st.session_state.query_engine = IntelligentQueryEngine()
                st.session_state.viz_engine = VisualizationEngine()
                st.session_state.schema_engine = SchemaDiscoveryEngine()
                logger.info("Core components initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize components: {e}")
                st.error(f"Failed to initialize core components: {e}")
        else:
            # Create mock objects for demo
            st.session_state.mongodb_manager = None
            st.session_state.query_engine = None
            st.session_state.viz_engine = None
            st.session_state.schema_engine = None

def main():
    """Main application entry point"""

    # Page configuration
    st.set_page_config(
        page_title="DataPilot - MongoDB Intelligence",
        page_icon="🚀",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state
    initialize_session_state()

    # Custom CSS for better UI
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    .status-card {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f4e79;
        margin: 1rem 0;
    }
    .query-box {
        background: #ffffff;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Main header
    st.markdown("""
    <div class="main-header">
        <h1>🚀 DataPilot</h1>
        <p>Intelligent MongoDB Query System with Natural Language Processing</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar for connection and settings
    render_sidebar()

    # Main content area
    render_main_content()

    # Footer
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #666;'>"
        "DataPilot v1.0 - Intelligent Database Querying System"
        "</div>",
        unsafe_allow_html=True
    )

def render_sidebar():
    """Render the sidebar with connection settings and controls"""
    with st.sidebar:
        st.markdown("### 🔧 Connection & Settings")

        # Connection status and form
        if MODULES_AVAILABLE and st.session_state.mongodb_manager:
            mongodb_manager = st.session_state.mongodb_manager

            # Connection status
            UIComponents.render_connection_status(mongodb_manager)

            # Connection form
            if UIComponents.render_connection_form(mongodb_manager):
                # Handle connection attempt
                connection_uri = st.session_state.get('connection_uri_input', '')
                if connection_uri:
                    with st.spinner("🔗 Connecting to MongoDB..."):
                        success = asyncio.run(mongodb_manager.connect(connection_uri))
                        if success:
                            st.session_state.connected = True
                            st.session_state.current_database = mongodb_manager.current_database
                            st.success("✅ Connected successfully!")
                            st.rerun()
                        else:
                            st.error("❌ Connection failed!")

            # Database selection
            if mongodb_manager.is_connected:
                UIComponents.render_database_selector(mongodb_manager)

        else:
            # Fallback UI when modules not available
            st.warning("⚠️ Core modules not available")
            if st.button("🎮 Demo Mode", type="primary"):
                st.session_state.demo_mode = True
                st.session_state.connected = True
                st.rerun()

        # Query history
        selected_query = UIComponents.render_query_history(st.session_state.query_history)
        if selected_query:
            st.session_state.main_query_input = selected_query
            st.rerun()

        # Settings panel
        settings = UIComponents.render_settings_panel()

        # Update session state with settings
        for key, value in settings.items():
            st.session_state[key] = value

        # Statistics
        if st.session_state.query_stats['total_queries'] > 0:
            UIComponents.render_stats_dashboard(st.session_state.query_stats)

def render_main_content():
    """Render the main content area"""

    # Quick start guide for new users
    if not st.session_state.get('connected', False):
        render_quick_start()
        return

    # Main query interface
    render_query_interface()

    # Results and visualization area
    render_results_area()

def render_quick_start():
    """Render quick start guide for new users"""
    st.markdown("### 🚀 Welcome to DataPilot!")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        #### What is DataPilot?
        DataPilot is an intelligent MongoDB query system that allows you to:

        - 🗣️ **Ask questions in natural language**
        - 🔍 **Automatically discover relevant data**
        - 📊 **Generate visualizations automatically**
        - 🚀 **Query across multiple databases**
        - 🧠 **Learn your database structure**
        """)

    with col2:
        st.markdown("""
        #### Example Queries
        Try asking questions like:

        - "Show me all products"
        - "What are the top 10 customers by sales?"
        - "Create a chart of orders by month"
        - "Find products without prices"
        - "Count users by registration date"
        """)

    # Demo mode
    st.markdown("### 🎮 Try Demo Mode")
    if st.button("🚀 Start Demo with Sample Data", type="primary"):
        st.session_state.connected = True
        st.session_state.current_database = "demo_ecommerce"
        st.rerun()

def render_query_interface():
    """Render the main query interface"""
    # Query input
    user_query = UIComponents.render_query_input(
        "Ask questions like: 'Show me top 10 products by price' or 'Create a chart of sales by month'"
    )

    # Get query suggestions if available
    suggestions = []
    if MODULES_AVAILABLE and st.session_state.query_engine:
        try:
            schema_info = ""
            if st.session_state.mongodb_manager and st.session_state.mongodb_manager.is_connected:
                schema_info = st.session_state.mongodb_manager.get_schema_summary()
            suggestions = st.session_state.query_engine.get_query_suggestions(user_query, schema_info)
        except Exception as e:
            logger.error(f"Error getting suggestions: {e}")

    # Query action buttons
    action = UIComponents.render_query_buttons(user_query, suggestions)

    # Handle actions
    if action == "execute" and user_query.strip():
        execute_query(user_query.strip())
    elif action == "example":
        examples = [
            "Show me all products",
            "Count orders by status",
            "Top 5 customers by total orders",
            "Products without stock",
            "Average order value by month",
            "Create a pie chart of categories",
            "Find users registered this month"
        ]
        selected_example = random.choice(examples)
        st.session_state.main_query_input = selected_example
        st.rerun()
    elif action == "suggest" and suggestions:
        # Show suggestions
        selected_suggestion = UIComponents.render_query_suggestions(suggestions)
        if selected_suggestion:
            st.session_state.main_query_input = selected_suggestion
            st.rerun()
    elif action == "clear":
        st.session_state.main_query_input = ""
        st.rerun()

def render_results_area():
    """Render the results and visualization area"""
    if st.session_state.chat_history:
        st.markdown("### 💬 Conversation History")

        for i, (query, response, viz) in enumerate(st.session_state.chat_history):
            with st.container():
                st.markdown(f"**You:** {query}")
                st.markdown(f"**DataPilot:** {response}")

                if viz:
                    st.plotly_chart(viz, use_container_width=True)

                st.markdown("---")

async def execute_query(user_query: str):
    """Execute a user query and display results"""
    start_time = time.time()

    try:
        # Update query stats
        st.session_state.query_stats['total_queries'] += 1

        with st.spinner("🧠 Analyzing your query..."):
            if MODULES_AVAILABLE and st.session_state.mongodb_manager and st.session_state.query_engine:
                # Real query processing
                result = await process_intelligent_query(user_query)
            else:
                # Demo mode processing
                result = process_demo_query(user_query)

            # Update stats
            if result['success']:
                st.session_state.query_stats['successful_queries'] += 1
                if result.get('visualization'):
                    st.session_state.query_stats['visualizations_created'] += 1
            else:
                st.session_state.query_stats['failed_queries'] += 1

            # Display result
            UIComponents.render_chat_message("user", user_query, "👤")
            UIComponents.render_chat_message("assistant", result['response'], "🤖")

            # Show visualization if available
            if result.get('visualization'):
                st.plotly_chart(result['visualization'], use_container_width=True)

            # Show query details if enabled
            if st.session_state.get('show_query_details', False):
                UIComponents.render_query_result(result)

            # Add to chat history
            st.session_state.chat_history.append((
                user_query,
                result['response'],
                result.get('visualization')
            ))
            st.session_state.query_history.append(user_query)

            # Clear input
            st.session_state.main_query_input = ""

            execution_time = time.time() - start_time
            st.success(f"✅ Query processed in {execution_time:.2f}s")

    except Exception as e:
        execution_time = time.time() - start_time
        st.session_state.query_stats['failed_queries'] += 1

        error_msg = f"Error processing query: {str(e)}"
        st.error(error_msg)
        logger.error(f"Query execution error: {traceback.format_exc()}")

        # Add error to chat history
        st.session_state.chat_history.append((
            user_query,
            f"❌ {error_msg}",
            None
        ))

def execute_query(user_query: str):
    """Wrapper to run async query execution"""
    asyncio.run(execute_query_async(user_query))

async def execute_query_async(user_query: str):
    """Async version of execute_query"""
    await execute_query(user_query)

async def process_intelligent_query(user_query: str) -> Dict[str, Any]:
    """Process query using intelligent query engine"""
    try:
        mongodb_manager = st.session_state.mongodb_manager
        query_engine = st.session_state.query_engine
        viz_engine = st.session_state.viz_engine

        # Get schema information
        schema_info = mongodb_manager.get_schema_summary()

        # Analyze the query
        analysis = await query_engine.analyze_query(user_query, schema_info)

        # Generate MongoDB query
        mongodb_query = await query_engine.generate_mongodb_query(analysis, schema_info)

        # Execute the query
        query_result = await mongodb_manager.execute_query(mongodb_query)

        if not query_result['success']:
            return {
                'success': False,
                'response': f"❌ Query execution failed: {query_result.get('error', 'Unknown error')}",
                'visualization': None,
                'execution_time': query_result.get('execution_time', 0)
            }

        # Generate natural language response
        response_text = f"Found {query_result.get('result_count', 0)} results"
        if query_result.get('results'):
            if isinstance(query_result['results'], list) and len(query_result['results']) > 0:
                response_text = f"✅ Found {len(query_result['results'])} results from your {mongodb_manager.current_database} database."
            else:
                response_text = f"✅ Query executed successfully: {query_result['results']}"

        # Create visualization if needed
        visualization = None
        if analysis.needs_visualization and query_result.get('results') and isinstance(query_result['results'], list):
            try:
                visualization = viz_engine.create_visualization(
                    query_result['results'],
                    user_query
                )
                if visualization:
                    response_text += " 📊 I've also created a visualization for you!"
            except Exception as e:
                logger.error(f"Visualization creation failed: {e}")

        return {
            'success': True,
            'response': response_text,
            'visualization': visualization,
            'execution_time': query_result.get('execution_time', 0),
            'mongodb_query': mongodb_query,
            'result_count': query_result.get('result_count', 0)
        }

    except Exception as e:
        logger.error(f"Intelligent query processing failed: {e}")
        return {
            'success': False,
            'response': f"❌ Query processing failed: {str(e)}",
            'visualization': None,
            'execution_time': 0
        }

def process_demo_query(user_query: str) -> Dict[str, Any]:
    """Process query in demo mode with mock data"""
    try:
        # Simulate processing time
        time.sleep(0.5)

        # Generate mock response based on query content
        query_lower = user_query.lower()

        if 'chart' in query_lower or 'visualization' in query_lower:
            response = f"🎨 I understand you want to create a visualization for: '{user_query}'. In demo mode, I would analyze your data and create an appropriate chart!"
        elif 'count' in query_lower:
            response = f"📊 I would count the relevant records for: '{user_query}'. Demo result: Found 42 matching records."
        elif 'top' in query_lower or 'best' in query_lower:
            response = f"🏆 I would find the top results for: '{user_query}'. Demo shows top performers in your dataset!"
        elif 'show' in query_lower or 'list' in query_lower:
            response = f"📋 I would display the data for: '{user_query}'. Demo mode shows sample records from your database."
        else:
            response = f"🤖 I understand your query: '{user_query}'. In a real database connection, I would analyze your data and provide specific results!"

        response += "\n\n💡 **Connect to your MongoDB database to see real results!**"

        return {
            'success': True,
            'response': response,
            'visualization': None,
            'execution_time': 0.5,
            'demo_mode': True
        }

    except Exception as e:
        return {
            'success': False,
            'response': f"❌ Demo query processing failed: {str(e)}",
            'visualization': None,
            'execution_time': 0
        }

if __name__ == "__main__":
    main()