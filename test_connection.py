#!/usr/bin/env python3
"""
Dynamic MongoDB Natural Language Query Engine
No hardcoding - learns from data and uses intelligent pattern matching
"""

import os
import sys
import json
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict, Counter
from datetime import datetime
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import argparse

try:
    from pymongo import MongoClient
    import openai
    from rich.console import Console
    from rich.table import Table
    from rich import print as rprint
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Install with: pip install pymongo openai rich scikit-learn numpy")
    sys.exit(1)

console = Console()


class DynamicSchemaLearner:
    """Learns schema patterns without hardcoding"""
    
    def __init__(self, db):
        self.db = db
        self.pattern_library = PatternLibrary()
        
    def learn_database_patterns(self, sample_size: int = 200) -> Dict[str, Any]:
        """Learn patterns from actual data without assumptions"""
        console.print("[bold blue]Learning database patterns...[/bold blue]")
        
        db_patterns = {
            "database_name": self.db.name,
            "collections": {},
            "learned_patterns": {},
            "relationships": [],
            "discovered_at": datetime.now().isoformat()
        }
        
        # Learn from each collection
        for collection_name in self.db.list_collection_names():
            if collection_name.startswith('system.'):
                continue
                
            collection = self.db[collection_name]
            patterns = self._learn_collection_patterns(collection, sample_size)
            db_patterns["collections"][collection_name] = patterns
        
        # Learn cross-collection patterns
        db_patterns["relationships"] = self._learn_relationships(db_patterns["collections"])
        db_patterns["learned_patterns"] = self._consolidate_patterns(db_patterns["collections"])
        
        return db_patterns
    
    def _learn_collection_patterns(self, collection, sample_size: int) -> Dict[str, Any]:
        """Learn patterns from a collection without assumptions"""
        # Get samples
        samples = list(collection.aggregate([
            {"$sample": {"size": min(sample_size, collection.count_documents({}))}}
        ]))
        
        if not samples:
            return {"document_count": 0, "patterns": {}}
        
        # Learn field patterns
        field_patterns = self._discover_field_patterns(samples)
        
        # Learn value patterns
        value_patterns = self._discover_value_patterns(samples, field_patterns)
        
        # Learn query patterns (if query logs available)
        query_patterns = self._learn_common_queries(collection)
        
        return {
            "document_count": collection.count_documents({}),
            "field_patterns": field_patterns,
            "value_patterns": value_patterns,
            "query_patterns": query_patterns,
            "sample_document": self._anonymize_document(samples[0]) if samples else None
        }
    
    def _discover_field_patterns(self, documents: List[Dict]) -> Dict[str, Any]:
        """Discover patterns in field names and structures"""
        field_info = defaultdict(lambda: {
            "occurrences": 0,
            "type_distribution": Counter(),
            "value_samples": [],
            "nested_structure": None,
            "statistical_profile": {}
        })
        
        for doc in documents:
            self._analyze_document_structure(doc, field_info, "")
        
        # Convert to patterns
        patterns = {}
        total_docs = len(documents)
        
        for field_path, info in field_info.items():
            if not field_path:
                continue
                
            # Statistical analysis of values
            patterns[field_path] = {
                "frequency": info["occurrences"] / total_docs,
                "type_profile": dict(info["type_distribution"]),
                "inferred_semantics": self._infer_semantics(
                    field_path, 
                    info["value_samples"], 
                    info["type_distribution"]
                ),
                "statistical_profile": self._compute_statistics(info["value_samples"])
            }
        
        return patterns
    
    def _infer_semantics(self, field_name: str, samples: List[Any], types: Counter) -> Dict[str, Any]:
        """Infer semantic meaning from data patterns, not hardcoded rules"""
        semantics = {
            "confidence": 0.0,
            "semantic_type": "unknown",
            "patterns": []
        }
        
        # Analyze actual values, not field names
        if not samples:
            return semantics
        
        # Convert samples to strings for pattern analysis
        str_samples = [str(s) for s in samples[:100]]  # Limit analysis
        
        # Check various patterns
        patterns_found = []
        
        # Email pattern
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if sum(1 for s in str_samples if re.match(email_pattern, s)) / len(str_samples) > 0.8:
            patterns_found.append(("email", 0.9))
        
        # URL pattern
        url_pattern = r'^https?://'
        if sum(1 for s in str_samples if re.match(url_pattern, s)) / len(str_samples) > 0.8:
            patterns_found.append(("url", 0.9))
        
        # Date patterns (various formats)
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}',  # ISO date
            r'^\d{2}/\d{2}/\d{4}',  # US date
            r'^\d{2}-\d{2}-\d{4}',  # Other date
        ]
        for pattern in date_patterns:
            if sum(1 for s in str_samples if re.match(pattern, s)) / len(str_samples) > 0.7:
                patterns_found.append(("date", 0.85))
                break
        
        # Numeric patterns
        if types.get('int', 0) + types.get('float', 0) > len(samples) * 0.9:
            # Check if it's likely a price (has decimal places consistently)
            if types.get('float', 0) > len(samples) * 0.7:
                decimal_places = [len(str(s).split('.')[-1]) for s in samples if isinstance(s, float)]
                if decimal_places and all(d == 2 for d in decimal_places[:10]):
                    patterns_found.append(("currency", 0.8))
            else:
                patterns_found.append(("numeric", 0.9))
        
        # ID pattern (consistent length, alphanumeric)
        if len(set(len(str(s)) for s in samples[:20])) == 1:  # Consistent length
            if all(re.match(r'^[a-zA-Z0-9]+$', str(s)) for s in samples[:20]):
                patterns_found.append(("identifier", 0.85))
        
        # Boolean pattern
        bool_values = {str(s).lower() for s in samples}
        if bool_values.issubset({'true', 'false', '0', '1', 'yes', 'no'}):
            patterns_found.append(("boolean", 0.95))
        
        # Select the highest confidence pattern
        if patterns_found:
            best_pattern = max(patterns_found, key=lambda x: x[1])
            semantics["semantic_type"] = best_pattern[0]
            semantics["confidence"] = best_pattern[1]
            semantics["patterns"] = [p[0] for p in patterns_found]
        
        return semantics
    
    def _compute_statistics(self, samples: List[Any]) -> Dict[str, Any]:
        """Compute statistical profile for numeric data"""
        stats = {}
        
        numeric_samples = [s for s in samples if isinstance(s, (int, float))]
        if numeric_samples:
            stats["numeric"] = {
                "min": min(numeric_samples),
                "max": max(numeric_samples),
                "mean": np.mean(numeric_samples),
                "median": np.median(numeric_samples),
                "std": np.std(numeric_samples)
            }
        
        string_samples = [s for s in samples if isinstance(s, str)]
        if string_samples:
            lengths = [len(s) for s in string_samples]
            stats["string"] = {
                "min_length": min(lengths),
                "max_length": max(lengths),
                "avg_length": np.mean(lengths),
                "unique_ratio": len(set(string_samples)) / len(string_samples)
            }
        
        return stats
    
    def _analyze_document_structure(self, obj: Any, field_info: Dict, parent_path: str):
        """Recursively analyze document structure"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                field_path = f"{parent_path}.{key}" if parent_path else key
                field_info[field_path]["occurrences"] += 1
                
                if value is not None:
                    field_info[field_path]["type_distribution"][type(value).__name__] += 1
                    
                    # Store samples (limit to prevent memory issues)
                    if len(field_info[field_path]["value_samples"]) < 100:
                        field_info[field_path]["value_samples"].append(value)
                    
                    if isinstance(value, dict):
                        self._analyze_document_structure(value, field_info, field_path)
                    elif isinstance(value, list) and value:
                        # Analyze array elements
                        for item in value[:5]:  # Sample first 5
                            self._analyze_document_structure(item, field_info, f"{field_path}[]")
    
    def _discover_value_patterns(self, documents: List[Dict], field_patterns: Dict) -> Dict[str, Any]:
        """Discover patterns in field values"""
        value_patterns = {}
        
        for field, pattern in field_patterns.items():
            if pattern["inferred_semantics"]["semantic_type"] != "unknown":
                value_patterns[field] = {
                    "semantic_type": pattern["inferred_semantics"]["semantic_type"],
                    "common_values": self._find_common_values(documents, field),
                    "value_distribution": pattern.get("statistical_profile", {})
                }
        
        return value_patterns
    
    def _find_common_values(self, documents: List[Dict], field_path: str) -> List[Tuple[Any, int]]:
        """Find most common values for a field"""
        values = Counter()
        
        for doc in documents:
            value = self._get_nested_value(doc, field_path)
            if value is not None:
                values[value] += 1
        
        return values.most_common(10)
    
    def _get_nested_value(self, doc: Dict, path: str) -> Any:
        """Get value from nested path"""
        parts = path.split('.')
        current = doc
        
        for part in parts:
            if part.endswith('[]'):
                part = part[:-2]
                if isinstance(current, dict) and part in current:
                    current = current[part]
                    if isinstance(current, list) and current:
                        return current[0]  # Return first element as sample
            elif isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None
        
        return current
    
    def _learn_relationships(self, collections: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Learn relationships between collections using data patterns"""
        relationships = []
        collection_names = list(collections.keys())
        
        for i, col1 in enumerate(collection_names):
            for col2 in collection_names[i+1:]:
                # Compare field patterns
                fields1 = collections[col1]["field_patterns"]
                fields2 = collections[col2]["field_patterns"]
                
                # Look for matching ID patterns
                for field1, pattern1 in fields1.items():
                    if pattern1["inferred_semantics"]["semantic_type"] == "identifier":
                        # Check if this ID pattern matches collection name
                        if col2.lower() in field1.lower() or col1.lower() in field1.lower():
                            relationships.append({
                                "from": col1,
                                "to": col2,
                                "field": field1,
                                "confidence": pattern1["inferred_semantics"]["confidence"],
                                "type": "foreign_key"
                            })
        
        return relationships
    
    def _learn_common_queries(self, collection) -> List[Dict[str, Any]]:
        """Learn from MongoDB logs or profiler if available"""
        # This would connect to MongoDB profiler in production
        # For now, return empty
        return []
    
    def _consolidate_patterns(self, collections: Dict[str, Any]) -> Dict[str, Any]:
        """Consolidate patterns across collections"""
        global_patterns = {
            "field_naming_conventions": self._learn_naming_conventions(collections),
            "common_structures": self._learn_common_structures(collections),
            "data_types_distribution": self._learn_type_distribution(collections)
        }
        return global_patterns
    
    def _learn_naming_conventions(self, collections: Dict[str, Any]) -> Dict[str, Any]:
        """Learn field naming patterns across database"""
        all_fields = []
        for col_data in collections.values():
            all_fields.extend(col_data["field_patterns"].keys())
        
        # Analyze naming patterns
        conventions = {
            "case_style": self._detect_case_style(all_fields),
            "separator": self._detect_separator(all_fields),
            "common_prefixes": self._find_common_prefixes(all_fields),
            "common_suffixes": self._find_common_suffixes(all_fields)
        }
        return conventions
    
    def _detect_case_style(self, fields: List[str]) -> str:
        """Detect predominant case style"""
        styles = {
            "camelCase": 0,
            "snake_case": 0,
            "PascalCase": 0,
            "kebab-case": 0
        }
        
        for field in fields:
            if '_' in field:
                styles["snake_case"] += 1
            elif '-' in field:
                styles["kebab-case"] += 1
            elif field[0].isupper():
                styles["PascalCase"] += 1
            elif any(c.isupper() for c in field[1:]):
                styles["camelCase"] += 1
        
        return max(styles, key=styles.get)
    
    def _detect_separator(self, fields: List[str]) -> str:
        """Detect field separator"""
        separators = Counter()
        for field in fields:
            if '_' in field:
                separators['_'] += 1
            if '-' in field:
                separators['-'] += 1
            if '.' in field:
                separators['.'] += 1
        
        return separators.most_common(1)[0][0] if separators else None
    
    def _find_common_prefixes(self, fields: List[str]) -> List[str]:
        """Find common field prefixes"""
        prefixes = Counter()
        for field in fields:
            parts = re.split(r'[_\-.]', field)
            if len(parts) > 1:
                prefixes[parts[0]] += 1
        
        return [p for p, count in prefixes.most_common(5) if count > 3]
    
    def _find_common_suffixes(self, fields: List[str]) -> List[str]:
        """Find common field suffixes"""
        suffixes = Counter()
        for field in fields:
            parts = re.split(r'[_\-.]', field)
            if len(parts) > 1:
                suffixes[parts[-1]] += 1
        
        return [s for s, count in suffixes.most_common(5) if count > 3]
    
    def _learn_common_structures(self, collections: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Learn common document structures"""
        structures = []
        
        # Find similar field sets across collections
        for col1, data1 in collections.items():
            fields1 = set(data1["field_patterns"].keys())
            
            for col2, data2 in collections.items():
                if col1 >= col2:
                    continue
                    
                fields2 = set(data2["field_patterns"].keys())
                common = fields1.intersection(fields2)
                
                if len(common) > 3:  # Significant overlap
                    similarity = len(common) / min(len(fields1), len(fields2))
                    structures.append({
                        "collections": [col1, col2],
                        "common_fields": list(common),
                        "similarity": similarity
                    })
        
        return sorted(structures, key=lambda x: x["similarity"], reverse=True)[:10]
    
    def _learn_type_distribution(self, collections: Dict[str, Any]) -> Dict[str, float]:
        """Learn overall type distribution"""
        type_counts = Counter()
        total_fields = 0
        
        for col_data in collections.values():
            for field_data in col_data["field_patterns"].values():
                for type_name, count in field_data["type_profile"].items():
                    type_counts[type_name] += count
                    total_fields += count
        
        return {t: count/total_fields for t, count in type_counts.items()} if total_fields > 0 else {}
    
    def _anonymize_document(self, doc: Dict) -> Dict:
        """Anonymize sensitive data in sample document"""
        if not doc:
            return {}
        
        anonymized = {}
        for key, value in doc.items():
            if isinstance(value, str):
                # Check if it looks sensitive
                if '@' in value:  # Email
                    anonymized[key] = "<EMAIL>"
                elif re.match(r'^\d{3}-\d{3}-\d{4}$', value):  # Phone
                    anonymized[key] = "************"
                else:
                    anonymized[key] = value[:20] + "..." if len(value) > 20 else value
            elif isinstance(value, dict):
                anonymized[key] = self._anonymize_document(value)
            elif isinstance(value, list) and value:
                anonymized[key] = [self._anonymize_document(value[0])] if isinstance(value[0], dict) else value[:3]
            else:
                anonymized[key] = value
        
        return anonymized


class PatternLibrary:
    """Library of learned query patterns"""
    
    def __init__(self):
        self.patterns = {
            "count": [],
            "filter": [],
            "aggregation": [],
            "sort": [],
            "join": []
        }
        self._initialize_base_patterns()
    
    def _initialize_base_patterns(self):
        """Initialize with minimal base patterns that will be expanded through learning"""
        # These are just seeds - the system will learn more patterns
        self.patterns["count"] = [
            r"how many",
            r"count of",
            r"number of",
            r"total"
        ]
        
        self.patterns["filter"] = [
            r"where .* (?:is|equals|=) (.*)",
            r"with .* (?:greater than|>|more than) (.*)",
            r"having .* (?:less than|<) (.*)"
        ]
        
        self.patterns["aggregation"] = [
            r"average|avg",
            r"sum|total",
            r"group by",
            r"per"
        ]
    
    def learn_pattern(self, query_type: str, pattern: str):
        """Add a learned pattern"""
        if query_type in self.patterns:
            self.patterns[query_type].append(pattern)


class IntelligentQueryGenerator:
    """Generates queries using learned patterns and semantic understanding"""
    
    def __init__(self, learned_patterns: Dict[str, Any]):
        self.patterns = learned_patterns
        self.vectorizer = TfidfVectorizer()
        self._prepare_semantic_index()
    
    def _prepare_semantic_index(self):
        """Prepare semantic index for field matching"""
        all_fields = []
        self.field_mapping = {}
        
        for collection, data in self.patterns["collections"].items():
            for field in data["field_patterns"].keys():
                all_fields.append(field)
                self.field_mapping[field] = collection
        
        if all_fields:
            # Create TF-IDF vectors for field names
            self.field_vectors = self.vectorizer.fit_transform(all_fields)
            self.field_names = all_fields
    
    def generate_query(self, natural_language: str) -> Dict[str, Any]:
        """Generate query using learned patterns"""
        # Clean and tokenize input
        nl_clean = natural_language.lower().strip()
        
        # Identify query intent
        intent = self._identify_intent(nl_clean)
        
        # Identify target collection
        collection = self._identify_collection(nl_clean)
        
        if not collection:
            return {
                "error": "Could not identify target collection",
                "suggestions": self._suggest_collections(nl_clean)
            }
        
        # Extract query components
        components = self._extract_query_components(nl_clean, collection, intent)
        
        # Build query
        query = self._build_query(intent, collection, components)
        
        return query
    
    def _identify_intent(self, query: str) -> str:
        """Identify query intent using learned patterns"""
        # Score each intent type
        scores = {}
        
        for intent_type, patterns in PatternLibrary().patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query, re.IGNORECASE):
                    score += 1
            scores[intent_type] = score
        
        # If no clear winner, analyze further
        if max(scores.values()) == 0:
            return self._deep_intent_analysis(query)
        
        return max(scores, key=scores.get)
    
    def _deep_intent_analysis(self, query: str) -> str:
        """Deeper analysis when simple pattern matching fails"""
        # Look for action verbs
        action_verbs = {
            "find": "filter",
            "show": "filter",
            "get": "filter",
            "list": "filter",
            "calculate": "aggregation",
            "compute": "aggregation",
            "analyze": "aggregation"
        }
        
        for verb, intent in action_verbs.items():
            if verb in query:
                return intent
        
        return "filter"  # Default
    
    def _identify_collection(self, query: str) -> Optional[str]:
        """Identify collection using semantic similarity"""
        # First try direct matching
        for collection in self.patterns["collections"]:
            if collection.lower() in query:
                return collection
            # Check singular/plural
            if collection.endswith('s') and collection[:-1].lower() in query:
                return collection
        
        # Use semantic similarity
        if hasattr(self, 'field_vectors'):
            query_vector = self.vectorizer.transform([query])
            similarities = cosine_similarity(query_vector, self.field_vectors)[0]
            
            # Find best matching field
            best_idx = similarities.argmax()
            if similarities[best_idx] > 0.3:  # Threshold
                best_field = self.field_names[best_idx]
                return self.field_mapping.get(best_field)
        
        return None
    
    def _suggest_collections(self, query: str) -> List[str]:
        """Suggest possible collections"""
        suggestions = []
        
        # Calculate similarity to each collection
        for collection in self.patterns["collections"]:
            # Simple word overlap
            collection_words = set(collection.lower().split('_'))
            query_words = set(query.lower().split())
            overlap = len(collection_words.intersection(query_words))
            
            if overlap > 0:
                suggestions.append((collection, overlap))
        
        return [s[0] for s in sorted(suggestions, key=lambda x: x[1], reverse=True)][:3]
    
    def _extract_query_components(self, query: str, collection: str, intent: str) -> Dict[str, Any]:
        """Extract query components based on learned patterns"""
        components = {
            "filters": {},
            "projections": [],
            "sort": {},
            "limit": None,
            "aggregations": []
        }
        
        collection_data = self.patterns["collections"][collection]
        
        # Extract filters
        for field, pattern in collection_data["field_patterns"].items():
            # Look for field mentions
            field_variations = self._generate_field_variations(field)
            
            for variation in field_variations:
                if variation in query:
                    # Try to extract value
                    value = self._extract_value_for_field(query, variation, pattern)
                    if value is not None:
                        components["filters"][field] = value
        
        # Extract sorting
        sort_patterns = [
            (r"sort by (\w+)", 1),
            (r"order by (\w+)", 1),
            (r"(\w+) ascending", -1),
            (r"(\w+) descending", -1)
        ]
        
        for pattern, direction in sort_patterns:
            match = re.search(pattern, query)
            if match:
                sort_field = self._match_field_name(match.group(1), collection)
                if sort_field:
                    components["sort"][sort_field] = direction
        
        # Extract limit
        limit_match = re.search(r"(?:top|first|limit) (\d+)", query)
        if limit_match:
            components["limit"] = int(limit_match.group(1))
        
        return components
    
    def _generate_field_variations(self, field: str) -> List[str]:
        """Generate variations of field name for matching"""
        variations = [field.lower()]
        
        # Handle different naming conventions
        if '_' in field:
            # snake_case to space
            variations.append(field.replace('_', ' ').lower())
        
        # camelCase to space
        camel_split = re.sub(r'([A-Z])', r' \1', field).strip().lower()
        variations.append(camel_split)
        
        return variations
    
    def _extract_value_for_field(self, query: str, field_mention: str, field_pattern: Dict) -> Any:
        """Extract value for a field based on its semantic type"""
        semantic_type = field_pattern["inferred_semantics"]["semantic_type"]
        
        # Look for value near field mention
        query_lower = query.lower()
        field_pos = query_lower.find(field_mention)
        
        if field_pos == -1:
            return None
        
        # Extract surrounding context
        context = query[max(0, field_pos - 20):field_pos + len(field_mention) + 50]
        
        if semantic_type == "numeric" or semantic_type == "currency":
            # Look for numbers
            numbers = re.findall(r'\b\d+\.?\d*\b', context)
            if numbers:
                return float(numbers[0]) if '.' in numbers[0] else int(numbers[0])
        
        elif semantic_type == "boolean":
            if any(word in context.lower() for word in ['true', 'yes', 'active', 'enabled']):
                return True
            elif any(word in context.lower() for word in ['false', 'no', 'inactive', 'disabled']):
                return False
        
        elif semantic_type in ["email", "url", "identifier"]:
            # Look for quoted strings
            quoted = re.findall(r'"([^"]*)"', context)
            if quoted:
                return quoted[0]
            
            # Look for specific patterns
            if semantic_type == "email":
                emails = re.findall(r'\b[\w.-]+@[\w.-]+\.\w+\b', context)
                if emails:
                    return emails[0]
        
        else:
            # General string - look for quoted or specific words after "is" or "equals"
            patterns = [
                rf"{field_mention}\s+(?:is|equals?|=)\s+['\"]?(\w+)['\"]?",
                rf"['\"]([^'\"]+)['\"]"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, context, re.IGNORECASE)
                if match:
                    return match.group(1)
        
        return None
    
    def _match_field_name(self, mention: str, collection: str) -> Optional[str]:
        """Match a mentioned field to actual field name"""
        collection_fields = self.patterns["collections"][collection]["field_patterns"].keys()
        
        # Direct match
        for field in collection_fields:
            if mention.lower() in field.lower():
                return field
        
        # Fuzzy match using similarity
        best_match = None
        best_score = 0
        
        for field in collection_fields:
            score = self._string_similarity(mention.lower(), field.lower())
            if score > best_score and score > 0.6:  # Threshold
                best_score = score
                best_match = field
        
        return best_match
    
    def _string_similarity(self, s1: str, s2: str) -> float:
        """Calculate string similarity"""
        # Simple character overlap
        set1 = set(s1)
        set2 = set(s2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0
    
    def _build_query(self, intent: str, collection: str, components: Dict) -> Dict[str, Any]:
        """Build final MongoDB query"""
        if intent == "count":
            return {
                "collection": collection,
                "operation": "count",
                "filter": components["filters"],
                "query": f"db.{collection}.countDocuments({json.dumps(components['filters'])})"
            }
        
        elif intent == "filter":
            query_obj = {
                "collection": collection,
                "operation": "find",
                "filter": components["filters"]
            }
            
            # Build query string
            query_str = f"db.{collection}.find({json.dumps(components['filters'])})"
            
            if components["sort"]:
                query_str += f".sort({json.dumps(components['sort'])})"
            
            if components["limit"]:
                query_str += f".limit({components['limit']})"
            
            query_obj["query"] = query_str
            return query_obj
        
        elif intent == "aggregation":
            pipeline = []
            
            if components["filters"]:
                pipeline.append({"$match": components["filters"]})
            
            # Build aggregation based on query
            # This is simplified - real implementation would be more sophisticated
            pipeline.append({
                "$group": {
                    "_id": None,
                    "result": {"$sum": 1}
                }
            })
            
            return {
                "collection": collection,
                "operation": "aggregate",
                "pipeline": pipeline,
                "query": f"db.{collection}.aggregate({json.dumps(pipeline, indent=2)})"
            }
        
        else:
            return {
                "collection": collection,
                "operation": "unknown",
                "components": components,
                "query": f"// Could not generate query for intent: {intent}"
            }


class DynamicMongoQueryTool:
    """Main tool with dynamic learning capabilities"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.client = None
        self.db = None
        self.learned_patterns = None
        self.query_generator = None
    
    def connect(self) -> bool:
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.connection_string, serverSelectionTimeoutMS=5000)
            self.client.server_info()
            
            # Extract database name
            parsed = urlparse(self.connection_string)
            db_name = parsed.path.strip('/')
            
            if not db_name:
                dbs = self.client.list_database_names()
                non_system_dbs = [db for db in dbs if db not in ['admin', 'local', 'config']]
                
                if len(non_system_dbs) == 1:
                    db_name = non_system_dbs[0]
                else:
                    console.print("[yellow]Multiple databases found:[/yellow]")
                    for db in non_system_dbs:
                        console.print(f"  - {db}")
                    db_name = input("Enter database name: ")
            
            self.db = self.client[db_name]
            console.print(f"[green]✓ Connected to database: {db_name}[/green]")
            return True
            
        except Exception as e:
            console.print(f"[red]✗ Error: {e}[/red]")
            return False
    
    def learn_patterns(self, sample_size: int = 200):
        """Learn patterns from the database"""
        if not self.db:
            console.print("[red]Not connected to database[/red]")
            return
        
        learner = DynamicSchemaLearner(self.db)
        self.learned_patterns = learner.learn_database_patterns(sample_size)
        
        # Initialize query generator with learned patterns
        self.query_generator = IntelligentQueryGenerator(self.learned_patterns)
        
        # Display learned patterns summary
        self._display_learned_patterns()
    
    def _display_learned_patterns(self):
        """Display summary of learned patterns"""
        console.print("\n[bold green]Learned Database Patterns[/bold green]")
        
        # Collections summary
        table = Table(title="Collections Overview")
        table.add_column("Collection", style="cyan")
        table.add_column("Documents", justify="right")
        table.add_column("Fields", justify="right")
        table.add_column("Key Patterns", style="yellow")
        
        for name, data in self.learned_patterns["collections"].items():
            key_patterns = []
            for field, pattern in list(data["field_patterns"].items())[:3]:
                semantic = pattern["inferred_semantics"]["semantic_type"]
                if semantic != "unknown":
                    key_patterns.append(f"{field} ({semantic})")
            
            table.add_row(
                name,
                f"{data['document_count']:,}",
                str(len(data["field_patterns"])),
                ", ".join(key_patterns[:2]) + "..." if len(key_patterns) > 2 else ", ".join(key_patterns)
            )
        
        console.print(table)
        
        # Naming conventions
        conventions = self.learned_patterns["learned_patterns"]["field_naming_conventions"]
        console.print(f"\n[bold]Discovered Naming Conventions:[/bold]")
        console.print(f"  Case style: {conventions['case_style']}")
        console.print(f"  Common prefixes: {', '.join(conventions['common_prefixes'][:3])}")
        
        # Relationships
        if self.learned_patterns["relationships"]:
            console.print(f"\n[bold]Discovered Relationships:[/bold]")
            for rel in self.learned_patterns["relationships"][:3]:
                console.print(f"  {rel['from']} → {rel['to']} (via {rel['field']})")
    
    def query(self, natural_language: str):
        """Process a natural language query"""
        if not self.query_generator:
            console.print("[red]Patterns not learned. Run learn_patterns() first.[/red]")
            return
        
        # Generate query
        result = self.query_generator.generate_query(natural_language)
        
        if "error" in result:
            console.print(f"[red]Error: {result['error']}[/red]")
            if "suggestions" in result:
                console.print(f"[yellow]Did you mean one of: {', '.join(result['suggestions'])}?[/yellow]")
            return
        
        # Display generated query
        console.print(f"\n[bold]Generated Query:[/bold]")
        console.print(f"[green]{result['query']}[/green]")
        
        # Ask for confirmation
        console.print("\n[bold]Execute? (y/n):[/bold] ", end="")
        if input().lower() == 'y':
            self._execute_query(result)
    
    def _execute_query(self, query_info: Dict[str, Any]):
        """Execute the generated query"""
        try:
            collection = self.db[query_info["collection"]]
            
            if query_info["operation"] == "find":
                results = list(collection.find(query_info["filter"]).limit(10))
                console.print(f"\n[green]Found {len(results)} documents[/green]")
                for i, doc in enumerate(results[:3]):
                    console.print(f"\n[cyan]Document {i+1}:[/cyan]")
                    console.print(json.dumps(doc, indent=2, default=str))
                
            elif query_info["operation"] == "count":
                count = collection.count_documents(query_info["filter"])
                console.print(f"\n[green]Count: {count:,}[/green]")
                
            elif query_info["operation"] == "aggregate":
                results = list(collection.aggregate(query_info["pipeline"]))
                console.print(f"\n[green]Aggregation results:[/green]")
                console.print(json.dumps(results, indent=2, default=str))
                
        except Exception as e:
            console.print(f"[red]Execution error: {e}[/red]")
    
    def interactive_mode(self):
        """Run in interactive mode"""
        console.print("\n[bold green]Dynamic MongoDB Query Tool[/bold green]")
        console.print("Learning from your data...\n")
        
        # Auto-learn patterns on startup
        self.learn_patterns()
        
        console.print("\n[bold]Ready! Ask questions in natural language.[/bold]")
        console.print("Type 'help' for commands, 'exit' to quit\n")
        
        while True:
            try:
                query = input("[bold]Query>[/bold] ").strip()
                
                if query.lower() == 'exit':
                    break
                elif query.lower() == 'help':
                    console.print("""
Commands:
  exit - Exit the tool
  help - Show this help
  relearn - Re-learn patterns with different sample size
  patterns - Show learned patterns
  
Example queries:
  "How many users do we have?"
  "Show me orders from last month"
  "What's the average price of products?"
  "Find customers with email containing gmail"
                    """)
                elif query.lower() == 'patterns':
                    self._display_learned_patterns()
                elif query.lower().startswith('relearn'):
                    parts = query.split()
                    sample_size = int(parts[1]) if len(parts) > 1 else 200
                    self.learn_patterns(sample_size)
                elif query:
                    self.query(query)
                    
            except KeyboardInterrupt:
                console.print("\n[yellow]Use 'exit' to quit[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")


def main():
    parser = argparse.ArgumentParser(description="Dynamic MongoDB Natural Language Query Tool")
    parser.add_argument("connection_string", help="MongoDB connection string")
    parser.add_argument("--sample-size", type=int, default=200, help="Sample size for pattern learning")
    
    args = parser.parse_args()
    
    # Create tool instance
    tool = DynamicMongoQueryTool(args.connection_string)
    
    # Connect to MongoDB
    if not tool.connect():
        sys.exit(1)
    
    # Run interactive mode
    tool.interactive_mode()
    
    # Clean up
    if tool.client:
        tool.client.close()


if __name__ == "__main__":
    main()