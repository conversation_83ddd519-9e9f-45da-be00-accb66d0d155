"""
Test script for Schema Discovery Engine
Demonstrates intelligent schema analysis and semantic understanding
"""

import asyncio
import json
from datetime import datetime
from mongodb_connection_manager import EnhancedMongoConnectionManager
from schema_discovery_engine import SchemaDiscoveryEngine

async def test_schema_discovery():
    """Test the schema discovery engine on real collections"""
    
    print("🧠 Testing Intelligent Schema Discovery Engine")
    print("=" * 70)
    
    # Initialize connection manager
    connection_manager = EnhancedMongoConnectionManager()
    
    try:
        # Connect to MongoDB
        print("\n1️⃣ Connecting to MongoDB...")
        connected = await connection_manager.connect()
        
        if not connected:
            print("❌ Failed to connect to MongoDB")
            return
        
        # Initialize schema discovery engine
        print("\n2️⃣ Initializing Schema Discovery Engine...")
        schema_engine = SchemaDiscoveryEngine(connection_manager)
        
        # Get available databases and collections
        databases = connection_manager.get_database_list()
        print(f"\n3️⃣ Found {len(databases)} databases for analysis")
        
        # Test schema discovery on interesting collections
        test_collections = [
            ("sample_mflix", "movies"),      # Rich movie data
            ("sample_mflix", "users"),       # User profiles
            ("comics-generator", "products"), # Product catalog
            ("database_assistant", "chat_messages"), # Chat data
            ("chat_sessions", "users")       # User data
        ]
        
        print("\n4️⃣ Analyzing Collection Schemas...")
        print("-" * 50)
        
        for db_name, collection_name in test_collections:
            # Check if collection exists
            available_collections = connection_manager.get_collection_list(db_name)
            if collection_name not in available_collections:
                print(f"⚠️  Collection {db_name}.{collection_name} not found, skipping...")
                continue
            
            print(f"\n🔍 Analyzing: {db_name}.{collection_name}")
            
            try:
                # Discover schema
                schema = await schema_engine.discover_collection_schema(
                    db_name, collection_name, sample_size=100
                )
                
                # Display results
                print(f"   📊 Documents: {schema.document_count:,}")
                print(f"   📋 Fields: {len(schema.fields)}")
                print(f"   🎯 Confidence: {schema.schema_confidence:.2f}")
                print(f"   🏢 Context: {schema.business_context}")
                
                # Show top fields
                print(f"   🔑 Key Fields:")
                sorted_fields = sorted(
                    schema.fields.values(), 
                    key=lambda x: x.frequency, 
                    reverse=True
                )
                
                for field in sorted_fields[:5]:
                    semantic_info = f" ({field.semantic_type.value})" if field.semantic_type.value != "unknown" else ""
                    print(f"      • {field.name}: {field.data_type}{semantic_info} "
                          f"[freq: {field.frequency:.2f}, unique: {field.uniqueness:.2f}]")
                
                # Show relationships
                if schema.relationships:
                    print(f"   🔗 Relationships:")
                    for rel in schema.relationships:
                        print(f"      • {rel['field']} → {rel['references']} ({rel['type']})")
                
                # Show sample values for interesting fields
                interesting_fields = [f for f in sorted_fields[:3] 
                                    if f.sample_values and f.semantic_type.value != "id"]
                
                if interesting_fields:
                    print(f"   📝 Sample Data:")
                    for field in interesting_fields:
                        sample_str = ", ".join(str(v)[:30] for v in field.sample_values[:3])
                        print(f"      • {field.name}: {sample_str}...")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {db_name}.{collection_name}: {str(e)}")
        
        # Test schema summary generation
        print("\n5️⃣ Schema Summary Examples...")
        print("-" * 50)
        
        for db_name, collection_name in test_collections[:2]:
            summary = schema_engine.get_schema_summary(db_name, collection_name)
            if summary:
                print(f"\n📋 Summary for {summary['collection']}:")
                print(json.dumps(summary, indent=2, default=str))
        
        # Test semantic type detection
        print("\n6️⃣ Semantic Type Detection Analysis...")
        print("-" * 50)
        
        semantic_stats = {}
        for schema in schema_engine.discovered_schemas.values():
            for field in schema.fields.values():
                semantic_type = field.semantic_type.value
                if semantic_type not in semantic_stats:
                    semantic_stats[semantic_type] = 0
                semantic_stats[semantic_type] += 1
        
        print("Detected semantic types:")
        for semantic_type, count in sorted(semantic_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {semantic_type}: {count} fields")
        
    except Exception as e:
        print(f"❌ Error during schema discovery testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        connection_manager.close_connection()
        print("\n✅ Schema discovery testing completed")

async def test_advanced_schema_features():
    """Test advanced schema discovery features"""
    
    print("\n" + "=" * 70)
    print("🚀 Testing Advanced Schema Features")
    print("=" * 70)
    
    connection_manager = EnhancedMongoConnectionManager()
    
    try:
        await connection_manager.connect()
        schema_engine = SchemaDiscoveryEngine(connection_manager)
        
        # Focus on the movies collection for detailed analysis
        print("\n🎬 Deep Analysis: sample_mflix.movies")
        
        schema = await schema_engine.discover_collection_schema(
            "sample_mflix", "movies", sample_size=500
        )
        
        print(f"\n📊 Detailed Schema Analysis:")
        print(f"   Total Documents: {schema.document_count:,}")
        print(f"   Sample Size: {schema.sample_size}")
        print(f"   Fields Discovered: {len(schema.fields)}")
        print(f"   Schema Confidence: {schema.schema_confidence:.3f}")
        
        # Analyze field types distribution
        type_distribution = {}
        semantic_distribution = {}
        
        for field in schema.fields.values():
            # Data type distribution
            if field.data_type not in type_distribution:
                type_distribution[field.data_type] = 0
            type_distribution[field.data_type] += 1
            
            # Semantic type distribution
            semantic_type = field.semantic_type.value
            if semantic_type not in semantic_distribution:
                semantic_distribution[semantic_type] = 0
            semantic_distribution[semantic_type] += 1
        
        print(f"\n📈 Data Type Distribution:")
        for data_type, count in sorted(type_distribution.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {data_type}: {count} fields")
        
        print(f"\n🧠 Semantic Type Distribution:")
        for semantic_type, count in sorted(semantic_distribution.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {semantic_type}: {count} fields")
        
        # Show fields with high confidence
        high_confidence_fields = [
            field for field in schema.fields.values() 
            if field.confidence_score > 0.8
        ]
        
        print(f"\n⭐ High Confidence Fields ({len(high_confidence_fields)}):")
        for field in sorted(high_confidence_fields, key=lambda x: x.confidence_score, reverse=True)[:10]:
            print(f"   • {field.name}: {field.semantic_type.value} "
                  f"(confidence: {field.confidence_score:.3f}, freq: {field.frequency:.2f})")
        
        # Show potential relationships
        if schema.relationships:
            print(f"\n🔗 Discovered Relationships:")
            for rel in schema.relationships:
                print(f"   • {rel}")
        
        # Analyze nested fields
        nested_fields = [field for field in schema.fields.values() if '.' in field.name]
        if nested_fields:
            print(f"\n🏗️ Nested Structure Analysis ({len(nested_fields)} nested fields):")
            nested_groups = {}
            for field in nested_fields:
                root = field.name.split('.')[0]
                if root not in nested_groups:
                    nested_groups[root] = []
                nested_groups[root].append(field.name)
            
            for root, fields in nested_groups.items():
                print(f"   • {root}: {len(fields)} nested fields")
                for field_name in fields[:3]:  # Show first 3
                    print(f"     - {field_name}")
                if len(fields) > 3:
                    print(f"     ... and {len(fields) - 3} more")
    
    except Exception as e:
        print(f"❌ Error during advanced testing: {str(e)}")
    
    finally:
        connection_manager.close_connection()

def run_schema_tests():
    """Run all schema discovery tests"""
    print("🧪 Starting Schema Discovery Engine Tests")
    print(f"⏰ Started at: {datetime.now()}")
    
    # Run basic schema discovery tests
    asyncio.run(test_schema_discovery())
    
    # Run advanced feature tests
    asyncio.run(test_advanced_schema_features())
    
    print(f"\n⏰ Completed at: {datetime.now()}")
    print("🎉 All schema discovery tests completed!")

if __name__ == "__main__":
    run_schema_tests()
