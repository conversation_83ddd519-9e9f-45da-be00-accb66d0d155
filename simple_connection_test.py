#!/usr/bin/env python3
"""
Simple MongoDB Connection Tester
Tests MongoDB connection and provides detailed debugging information
"""

import sys
import json
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, ConfigurationError
import dns.resolver
import socket

def test_dns_resolution(hostname):
    """Test DNS resolution for MongoDB hostname"""
    print(f"🔍 Testing DNS resolution for: {hostname}")
    
    try:
        # Test basic DNS resolution
        ip = socket.gethostbyname(hostname)
        print(f"✅ DNS resolution successful: {hostname} -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        
        # Try SRV record lookup for MongoDB Atlas
        if 'mongodb.net' in hostname:
            try:
                srv_records = dns.resolver.resolve(f"_mongodb._tcp.{hostname}", 'SRV')
                print(f"✅ SRV records found: {len(srv_records)} records")
                for record in srv_records:
                    print(f"   - {record.target}:{record.port}")
                return True
            except Exception as srv_e:
                print(f"❌ SRV lookup failed: {srv_e}")
        
        return False

def parse_connection_string(uri):
    """Parse and validate MongoDB connection string"""
    print(f"🔗 Parsing connection string...")
    
    if not uri.startswith(('mongodb://', 'mongodb+srv://')):
        print("❌ Invalid URI format. Must start with mongodb:// or mongodb+srv://")
        return None
    
    # Extract hostname
    if uri.startswith('mongodb+srv://'):
        # Extract hostname from SRV URI
        parts = uri.split('@')
        if len(parts) != 2:
            print("❌ Invalid SRV URI format")
            return None
        
        hostname_part = parts[1].split('/')[0]
        hostname = hostname_part.split('?')[0]  # Remove query parameters
        
        print(f"📍 Extracted hostname: {hostname}")
        return {
            'type': 'srv',
            'hostname': hostname,
            'full_uri': uri
        }
    
    else:
        # Regular mongodb:// URI
        parts = uri.split('@')
        if len(parts) == 2:
            hostname_part = parts[1].split('/')[0]
        else:
            hostname_part = uri.replace('mongodb://', '').split('/')[0]
        
        hostname = hostname_part.split(':')[0]  # Remove port
        
        print(f"📍 Extracted hostname: {hostname}")
        return {
            'type': 'standard',
            'hostname': hostname,
            'full_uri': uri
        }

def test_mongodb_connection(uri):
    """Test MongoDB connection with detailed error reporting"""
    print(f"\n🚀 Testing MongoDB connection...")
    print(f"URI: {uri[:50]}{'...' if len(uri) > 50 else ''}")
    
    try:
        # Create client with short timeout for testing
        client = MongoClient(
            uri,
            serverSelectionTimeoutMS=10000,  # 10 seconds
            connectTimeoutMS=10000,
            socketTimeoutMS=10000
        )
        
        # Test connection
        print("⏳ Attempting to connect...")
        client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # List databases
        print("\n📚 Available databases:")
        dbs = client.list_database_names()
        for db in dbs:
            if db not in ['admin', 'local', 'config']:
                doc_count = 0
                try:
                    collections = client[db].list_collection_names()
                    for col in collections:
                        doc_count += client[db][col].estimated_document_count()
                    print(f"   - {db} ({len(collections)} collections, ~{doc_count:,} documents)")
                except:
                    print(f"   - {db} (access limited)")
        
        client.close()
        return True
        
    except ServerSelectionTimeoutError as e:
        print(f"❌ Server selection timeout: {e}")
        print("💡 This usually means:")
        print("   - Network connectivity issues")
        print("   - Incorrect hostname/port")
        print("   - Firewall blocking connection")
        return False
        
    except ConnectionFailure as e:
        print(f"❌ Connection failure: {e}")
        print("💡 This usually means:")
        print("   - Authentication failed")
        print("   - SSL/TLS configuration issues")
        print("   - Server is down")
        return False
        
    except ConfigurationError as e:
        print(f"❌ Configuration error: {e}")
        print("💡 Check your connection string format")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def suggest_fixes(uri_info):
    """Suggest potential fixes based on the URI"""
    print("\n🔧 Troubleshooting suggestions:")
    
    if uri_info and uri_info['type'] == 'srv':
        print("For MongoDB Atlas (SRV) connections:")
        print("1. Verify your cluster is running and accessible")
        print("2. Check your IP whitelist in Atlas Security settings")
        print("3. Ensure your username/password are correct")
        print("4. Try connecting from Atlas Connect button for latest URI")
        
        # Suggest alternative connection string
        hostname = uri_info['hostname']
        if 'cluster0' in hostname:
            alt_hostname = hostname.replace('cluster0', 'cluster0-shard-00-00')
            print(f"\n💡 Try this alternative format:")
            print(f"mongodb://<username>:<password>@{alt_hostname}:27017/<database>?ssl=true&authSource=admin")
    
    else:
        print("For standard MongoDB connections:")
        print("1. Verify MongoDB server is running")
        print("2. Check hostname and port are correct")
        print("3. Ensure network connectivity")
        print("4. Verify authentication credentials")

def main():
    if len(sys.argv) != 2:
        print("Usage: python simple_connection_test.py <mongodb_uri>")
        print("\nExample:")
        print("python simple_connection_test.py 'mongodb+srv://user:<EMAIL>/database'")
        sys.exit(1)
    
    uri = sys.argv[1]
    
    print("🧪 MongoDB Connection Diagnostic Tool")
    print("=" * 50)
    
    # Step 1: Parse connection string
    uri_info = parse_connection_string(uri)
    
    if not uri_info:
        print("❌ Failed to parse connection string")
        sys.exit(1)
    
    # Step 2: Test DNS resolution
    dns_ok = test_dns_resolution(uri_info['hostname'])
    
    # Step 3: Test MongoDB connection
    if dns_ok:
        connection_ok = test_mongodb_connection(uri)
        
        if connection_ok:
            print("\n🎉 All tests passed! Your MongoDB connection is working.")
        else:
            suggest_fixes(uri_info)
    else:
        print("\n❌ DNS resolution failed. Cannot proceed with connection test.")
        suggest_fixes(uri_info)
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
