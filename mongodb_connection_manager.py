"""
Enhanced MongoDB Connection Manager for Chatbot System
Supports multi-database connections, intelligent routing, and schema discovery
"""

import pymongo
from pymongo import MongoClient
from typing import Dict, List, Any, Optional, Tuple, Set, Union
import json
import logging
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
import time
import traceback
import async<PERSON>
from concurrent.futures import ThreadPoolExecutor
import hashlib
from collections import defaultdict, Counter
import statistics
import re
from enum import Enum
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConnectionStatus(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class QueryType(Enum):
    FIND = "find"
    AGGREGATE = "aggregate"
    COUNT = "count"
    DISTINCT = "distinct"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"

@dataclass
class ConnectionConfig:
    """Configuration for MongoDB connections"""
    uri: str
    max_pool_size: int = 100
    min_pool_size: int = 10
    max_idle_time_ms: int = 30000
    server_selection_timeout_ms: int = 10000
    connect_timeout_ms: int = 10000
    socket_timeout_ms: int = 20000
    retry_writes: bool = True
    read_preference: str = "primary"

@dataclass
class DatabaseMetrics:
    """Performance and usage metrics for a database"""
    query_count: int = 0
    avg_response_time: float = 0.0
    error_count: int = 0
    last_accessed: Optional[datetime] = None
    popular_collections: List[str] = field(default_factory=list)
    query_patterns: Dict[str, int] = field(default_factory=dict)

@dataclass
class CollectionMetadata:
    """Enhanced metadata for collections"""
    name: str
    database: str
    document_count: int
    avg_document_size: int
    total_size: int
    indexes: List[Dict[str, Any]] = field(default_factory=list)
    schema_sample: Dict[str, Any] = field(default_factory=dict)
    field_types: Dict[str, str] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)
    access_frequency: int = 0
    data_quality_score: float = 0.0

class EnhancedMongoConnectionManager:
    """
    Enhanced MongoDB Connection Manager with intelligent features:
    - Multi-database support
    - Connection pooling and health monitoring
    - Schema discovery and caching
    - Query routing and optimization
    - Performance metrics tracking
    """
    
    def __init__(self, connection_uri: Optional[str] = None):
        self.connection_uri = connection_uri or os.getenv('MONGODB_URI')
        if not self.connection_uri:
            raise ValueError("MongoDB URI must be provided or set in MONGODB_URI environment variable")
        
        self.config = ConnectionConfig(uri=self.connection_uri)
        self.client: Optional[MongoClient] = None
        self.status = ConnectionStatus.DISCONNECTED
        
        # Metadata storage
        self.databases: Dict[str, Dict[str, Any]] = {}
        self.collections: Dict[str, CollectionMetadata] = {}
        self.metrics: Dict[str, DatabaseMetrics] = defaultdict(DatabaseMetrics)
        
        # Caching
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
        self.query_cache: Dict[str, Any] = {}
        self.cache_ttl = timedelta(hours=1)
        
        # Performance tracking
        self.query_history: List[Dict[str, Any]] = []
        self.connection_pool_stats: Dict[str, Any] = {}
        
        logger.info("Enhanced MongoDB Connection Manager initialized")

    async def connect(self) -> bool:
        """Establish connection to MongoDB cluster with enhanced error handling"""
        try:
            self.status = ConnectionStatus.CONNECTING
            logger.info("Connecting to MongoDB cluster...")
            
            # Create client with optimized settings
            self.client = MongoClient(
                self.config.uri,
                maxPoolSize=self.config.max_pool_size,
                minPoolSize=self.config.min_pool_size,
                maxIdleTimeMS=self.config.max_idle_time_ms,
                serverSelectionTimeoutMS=self.config.server_selection_timeout_ms,
                connectTimeoutMS=self.config.connect_timeout_ms,
                socketTimeoutMS=self.config.socket_timeout_ms,
                retryWrites=self.config.retry_writes
            )
            
            # Test connection
            await self._test_connection()
            
            # Initialize cluster discovery
            await self._discover_cluster()
            
            self.status = ConnectionStatus.CONNECTED
            logger.info("✅ Successfully connected to MongoDB cluster")
            return True
            
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            logger.error(f"❌ Failed to connect to MongoDB: {str(e)}")
            return False

    async def _test_connection(self):
        """Test MongoDB connection with ping"""
        try:
            # Use asyncio to run the blocking ping command
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.client.admin.command, 'ping')
            logger.info("MongoDB ping successful")
        except Exception as e:
            raise ConnectionError(f"MongoDB connection test failed: {str(e)}")

    async def _discover_cluster(self):
        """Discover and catalog all databases and collections"""
        try:
            logger.info("🔍 Discovering cluster topology...")
            
            # Get all databases
            loop = asyncio.get_event_loop()
            db_names = await loop.run_in_executor(None, self.client.list_database_names)
            
            # Filter out system databases
            user_databases = [db for db in db_names if db not in ['admin', 'local', 'config']]
            
            logger.info(f"Found {len(user_databases)} user databases: {user_databases}")
            
            # Discover collections in each database
            for db_name in user_databases:
                await self._discover_database(db_name)
                
            logger.info("✅ Cluster discovery completed")
            
        except Exception as e:
            logger.error(f"Error during cluster discovery: {str(e)}")
            raise

    async def _discover_database(self, db_name: str):
        """Discover and analyze a specific database"""
        try:
            loop = asyncio.get_event_loop()
            db = self.client[db_name]
            
            # Get collection names
            collection_names = await loop.run_in_executor(None, db.list_collection_names)
            
            # Initialize database info
            self.databases[db_name] = {
                'name': db_name,
                'collections': collection_names,
                'total_collections': len(collection_names),
                'discovered_at': datetime.now(),
                'last_accessed': None
            }
            
            logger.info(f"📁 Database '{db_name}': {len(collection_names)} collections")
            
            # Analyze each collection (sample for performance)
            for collection_name in collection_names[:10]:  # Limit initial discovery
                await self._analyze_collection(db_name, collection_name)
                
        except Exception as e:
            logger.error(f"Error discovering database {db_name}: {str(e)}")

    async def _analyze_collection(self, db_name: str, collection_name: str):
        """Analyze collection structure and metadata"""
        try:
            collection_key = f"{db_name}.{collection_name}"
            collection = self.client[db_name][collection_name]
            
            loop = asyncio.get_event_loop()
            
            # Get basic stats
            stats = await loop.run_in_executor(None, collection.estimated_document_count)
            
            # Sample documents for schema inference
            sample_docs = await loop.run_in_executor(
                None, 
                lambda: list(collection.find().limit(10))
            )
            
            # Get indexes
            indexes = await loop.run_in_executor(
                None,
                lambda: list(collection.list_indexes())
            )
            
            # Create collection metadata
            metadata = CollectionMetadata(
                name=collection_name,
                database=db_name,
                document_count=stats,
                avg_document_size=0,  # Will calculate if needed
                total_size=0,  # Will calculate if needed
                indexes=[dict(idx) for idx in indexes],
                schema_sample=sample_docs[0] if sample_docs else {},
                field_types=self._infer_field_types(sample_docs)
            )
            
            self.collections[collection_key] = metadata
            logger.info(f"   📊 Collection '{collection_name}': {stats} documents")
            
        except Exception as e:
            logger.error(f"Error analyzing collection {db_name}.{collection_name}: {str(e)}")

    def _infer_field_types(self, sample_docs: List[Dict[str, Any]]) -> Dict[str, str]:
        """Infer field types from sample documents"""
        field_types = {}
        
        for doc in sample_docs:
            for field, value in doc.items():
                if field not in field_types:
                    field_types[field] = type(value).__name__
                    
        return field_types

    def get_database_list(self) -> List[str]:
        """Get list of available databases"""
        return list(self.databases.keys())

    def get_collection_list(self, database: Optional[str] = None) -> List[str]:
        """Get list of collections, optionally filtered by database"""
        if database:
            db_info = self.databases.get(database, {})
            return db_info.get('collections', [])
        else:
            # Return all collections across all databases
            all_collections = []
            for db_info in self.databases.values():
                collections = db_info.get('collections', [])
                all_collections.extend([f"{db_info['name']}.{col}" for col in collections])
            return all_collections

    def get_collection_metadata(self, database: str, collection: str) -> Optional[CollectionMetadata]:
        """Get metadata for a specific collection"""
        collection_key = f"{database}.{collection}"
        return self.collections.get(collection_key)

    def get_cluster_summary(self) -> Dict[str, Any]:
        """Get comprehensive cluster summary"""
        total_collections = sum(len(db['collections']) for db in self.databases.values())
        total_documents = sum(
            metadata.document_count for metadata in self.collections.values()
        )
        
        return {
            'status': self.status.value,
            'total_databases': len(self.databases),
            'total_collections': total_collections,
            'total_documents': total_documents,
            'databases': {
                name: {
                    'collections': len(info['collections']),
                    'discovered_at': info['discovered_at'].isoformat()
                }
                for name, info in self.databases.items()
            },
            'connection_config': {
                'max_pool_size': self.config.max_pool_size,
                'server_selection_timeout': self.config.server_selection_timeout_ms
            }
        }

    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            self.status = ConnectionStatus.DISCONNECTED
            logger.info("MongoDB connection closed")
