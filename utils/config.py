"""
Configuration management for DataPilot
Handles environment variables, settings, and application configuration
"""

import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """MongoDB database configuration"""
    uri: str
    database_name: Optional[str] = None
    connection_timeout: int = 30000
    server_selection_timeout: int = 30000
    max_pool_size: int = 100
    min_pool_size: int = 10
    max_idle_time: int = 30000
    retry_writes: bool = True

@dataclass
class AIConfig:
    """AI/LLM configuration"""
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    default_model: str = "gpt-3.5-turbo"
    max_tokens: int = 2000
    temperature: float = 0.1
    timeout: int = 30

@dataclass
class CacheConfig:
    """Caching configuration"""
    enabled: bool = True
    redis_url: Optional[str] = None
    ttl_seconds: int = 3600
    max_memory_cache_size: int = 1000

@dataclass
class SecurityConfig:
    """Security configuration"""
    secret_key: str = "your-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    enable_authentication: bool = False
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])

@dataclass
class AppConfig:
    """Main application configuration"""
    debug: bool = False
    log_level: str = "INFO"
    max_query_results: int = 1000
    enable_query_logging: bool = True
    enable_auto_visualization: bool = True
    supported_chart_types: List[str] = field(default_factory=lambda: [
        "bar", "line", "pie", "scatter", "histogram", "box"
    ])

class Config:
    """Main configuration class that loads and manages all settings"""
    
    def __init__(self):
        """Initialize configuration from environment variables"""
        self.database = self._load_database_config()
        self.ai = self._load_ai_config()
        self.cache = self._load_cache_config()
        self.security = self._load_security_config()
        self.app = self._load_app_config()
        
        # Validate configuration
        self._validate_config()
        
        logger.info("Configuration loaded successfully")
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration from environment"""
        return DatabaseConfig(
            uri=os.getenv('MONGODB_URI', 'mongodb://localhost:27017'),
            database_name=os.getenv('MONGODB_DATABASE'),
            connection_timeout=int(os.getenv('MONGODB_CONNECTION_TIMEOUT', '30000')),
            server_selection_timeout=int(os.getenv('MONGODB_SERVER_SELECTION_TIMEOUT', '30000')),
            max_pool_size=int(os.getenv('MONGODB_MAX_POOL_SIZE', '100')),
            min_pool_size=int(os.getenv('MONGODB_MIN_POOL_SIZE', '10')),
            max_idle_time=int(os.getenv('MONGODB_MAX_IDLE_TIME', '30000')),
            retry_writes=os.getenv('MONGODB_RETRY_WRITES', 'true').lower() == 'true'
        )
    
    def _load_ai_config(self) -> AIConfig:
        """Load AI configuration from environment"""
        return AIConfig(
            openai_api_key=os.getenv('OPENAI_API_KEY'),
            anthropic_api_key=os.getenv('ANTHROPIC_API_KEY'),
            default_model=os.getenv('DEFAULT_AI_MODEL', 'gpt-3.5-turbo'),
            max_tokens=int(os.getenv('AI_MAX_TOKENS', '2000')),
            temperature=float(os.getenv('AI_TEMPERATURE', '0.1')),
            timeout=int(os.getenv('AI_TIMEOUT', '30'))
        )
    
    def _load_cache_config(self) -> CacheConfig:
        """Load cache configuration from environment"""
        return CacheConfig(
            enabled=os.getenv('CACHE_ENABLED', 'true').lower() == 'true',
            redis_url=os.getenv('REDIS_URL'),
            ttl_seconds=int(os.getenv('CACHE_TTL_SECONDS', '3600')),
            max_memory_cache_size=int(os.getenv('MAX_MEMORY_CACHE_SIZE', '1000'))
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """Load security configuration from environment"""
        allowed_origins = os.getenv('ALLOWED_ORIGINS', '*').split(',')
        return SecurityConfig(
            secret_key=os.getenv('SECRET_KEY', 'your-secret-key-change-in-production'),
            jwt_algorithm=os.getenv('JWT_ALGORITHM', 'HS256'),
            jwt_expiration_hours=int(os.getenv('JWT_EXPIRATION_HOURS', '24')),
            enable_authentication=os.getenv('ENABLE_AUTHENTICATION', 'false').lower() == 'true',
            allowed_origins=allowed_origins
        )
    
    def _load_app_config(self) -> AppConfig:
        """Load application configuration from environment"""
        chart_types = os.getenv('SUPPORTED_CHART_TYPES', 'bar,line,pie,scatter,histogram,box').split(',')
        return AppConfig(
            debug=os.getenv('DEBUG', 'false').lower() == 'true',
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            max_query_results=int(os.getenv('MAX_QUERY_RESULTS', '1000')),
            enable_query_logging=os.getenv('ENABLE_QUERY_LOGGING', 'true').lower() == 'true',
            enable_auto_visualization=os.getenv('ENABLE_AUTO_VISUALIZATION', 'true').lower() == 'true',
            supported_chart_types=[chart.strip() for chart in chart_types]
        )
    
    def _validate_config(self):
        """Validate configuration settings"""
        errors = []
        
        # Validate database URI
        if not self.database.uri:
            errors.append("MONGODB_URI is required")
        
        # Validate AI configuration
        if not self.ai.openai_api_key and not self.ai.anthropic_api_key:
            logger.warning("No AI API keys configured. AI features will be limited.")
        
        # Validate security in production
        if not self.app.debug and self.security.secret_key == "your-secret-key-change-in-production":
            errors.append("SECRET_KEY must be changed in production")
        
        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")
    
    def get_mongodb_uri(self) -> str:
        """Get MongoDB connection URI"""
        return self.database.uri
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI configuration as dictionary"""
        return {
            'openai_api_key': self.ai.openai_api_key,
            'anthropic_api_key': self.ai.anthropic_api_key,
            'default_model': self.ai.default_model,
            'max_tokens': self.ai.max_tokens,
            'temperature': self.ai.temperature,
            'timeout': self.ai.timeout
        }
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        return self.app.debug
    
    def get_log_level(self) -> str:
        """Get logging level"""
        return self.app.log_level
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            'database': {
                'uri': '***HIDDEN***' if self.database.uri else None,
                'database_name': self.database.database_name,
                'connection_timeout': self.database.connection_timeout,
                'max_pool_size': self.database.max_pool_size
            },
            'ai': {
                'has_openai_key': bool(self.ai.openai_api_key),
                'has_anthropic_key': bool(self.ai.anthropic_api_key),
                'default_model': self.ai.default_model,
                'max_tokens': self.ai.max_tokens,
                'temperature': self.ai.temperature
            },
            'cache': {
                'enabled': self.cache.enabled,
                'has_redis': bool(self.cache.redis_url),
                'ttl_seconds': self.cache.ttl_seconds
            },
            'app': {
                'debug': self.app.debug,
                'log_level': self.app.log_level,
                'max_query_results': self.app.max_query_results,
                'supported_chart_types': self.app.supported_chart_types
            }
        }

# Global configuration instance
config = Config()
