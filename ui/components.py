"""
UI Components for DataPilot Streamlit Application
Reusable UI components and utilities
"""

import streamlit as st
import logging
from typing import Dict, Any, List, Optional, Callable
import time
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class UIComponents:
    """Collection of reusable UI components for DataPilot"""
    
    @staticmethod
    def render_connection_status(mongodb_manager) -> None:
        """Render connection status indicator"""
        if mongodb_manager.is_connected:
            st.success(
                f"✅ **Connected to: {mongodb_manager.current_database}** | "
                f"{len(mongodb_manager.get_collections())} collections"
            )
        else:
            st.warning("⚠️ **Not connected to MongoDB**")
    
    @staticmethod
    def render_connection_form(mongodb_manager) -> bool:
        """
        Render MongoDB connection form
        
        Returns:
            bool: True if connection was attempted, False otherwise
        """
        with st.expander("🔗 MongoDB Connection", expanded=not mongodb_manager.is_connected):
            st.markdown("### Connection Settings")
            
            # Connection URI input
            connection_uri = st.text_input(
                "MongoDB URI",
                value=st.session_state.get('mongodb_uri', ''),
                type="password",
                help="mongodb://localhost:27017 or mongodb+srv://username:<EMAIL>/database",
                key="connection_uri_input"
            )
            
            # Quick connection examples
            st.markdown("**Quick Examples:**")
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("🏠 Local MongoDB"):
                    st.session_state.connection_uri_input = "mongodb://localhost:27017"
                    st.rerun()
            
            with col2:
                if st.button("☁️ MongoDB Atlas"):
                    st.session_state.connection_uri_input = "mongodb+srv://username:<EMAIL>/"
                    st.rerun()
            
            # Connection button
            col1, col2 = st.columns([1, 1])
            
            with col1:
                connect_clicked = st.button(
                    "🔗 Connect", 
                    type="primary", 
                    disabled=not connection_uri,
                    use_container_width=True
                )
            
            with col2:
                if st.button("🎮 Demo Mode", use_container_width=True):
                    st.session_state.demo_mode = True
                    st.session_state.connected = True
                    st.session_state.current_database = "demo_ecommerce"
                    st.success("Demo mode activated!")
                    st.rerun()
            
            if connect_clicked and connection_uri:
                return True
        
        return False
    
    @staticmethod
    def render_database_selector(mongodb_manager) -> None:
        """Render database selection dropdown"""
        if mongodb_manager.is_connected:
            # Get databases from connection info
            connection_info = mongodb_manager.connection_info
            databases = connection_info.get('user_databases', [])

            if databases:
                # Get current selection from session state or use current database
                current_selected = st.session_state.get('selected_database')
                if not current_selected:
                    current_selected = mongodb_manager.current_database
                    if current_selected:
                        st.session_state.selected_database = current_selected

                # If still no selection, use first database
                if not current_selected or current_selected not in databases:
                    current_selected = databases[0]
                    st.session_state.selected_database = current_selected

                current_index = databases.index(current_selected) if current_selected in databases else 0

                selected_db = st.selectbox(
                    "📊 Select Database",
                    databases,
                    index=current_index,
                    key="database_selector"
                )

                # Update both session state and mongodb manager if changed
                if selected_db != st.session_state.get('selected_database'):
                    st.session_state.selected_database = selected_db
                    if mongodb_manager.switch_database(selected_db):
                        st.success(f"✅ Switched to database: {selected_db}")
                        st.rerun()
                    else:
                        st.error(f"❌ Failed to switch to database: {selected_db}")
            else:
                st.info("No user databases found")
    
    @staticmethod
    def render_query_history(query_history: List[str], max_items: int = 5) -> Optional[str]:
        """
        Render query history sidebar
        
        Returns:
            str: Selected query from history, or None
        """
        st.markdown("### 📝 Recent Queries")
        
        if not query_history:
            st.info("No recent queries")
            return None
        
        selected_query = None
        
        # Show recent queries
        for i, query in enumerate(query_history[-max_items:]):
            # Truncate long queries
            display_query = query[:50] + "..." if len(query) > 50 else query
            
            if st.button(
                f"🔄 {display_query}",
                key=f"history_query_{i}",
                help=query,
                use_container_width=True
            ):
                selected_query = query
        
        # Clear history button
        if st.button("🗑️ Clear History", use_container_width=True):
            st.session_state.query_history = []
            st.rerun()
        
        return selected_query
    
    @staticmethod
    def render_settings_panel() -> Dict[str, Any]:
        """
        Render settings panel
        
        Returns:
            Dict with current settings
        """
        with st.expander("⚙️ Settings"):
            settings = {}
            
            # Visualization settings
            st.markdown("**Visualization**")
            settings['auto_visualization'] = st.checkbox(
                "Enable Auto-visualization",
                value=st.session_state.get('auto_visualization', True),
                key="auto_viz_setting"
            )
            
            settings['chart_theme'] = st.selectbox(
                "Chart Theme",
                ["plotly", "plotly_white", "plotly_dark", "ggplot2", "seaborn"],
                index=0,
                key="chart_theme_setting"
            )
            
            # Query settings
            st.markdown("**Query Execution**")
            settings['max_results'] = st.slider(
                "Max Results",
                min_value=10,
                max_value=1000,
                value=st.session_state.get('max_results', 100),
                step=10,
                key="max_results_setting"
            )
            
            settings['show_query_details'] = st.checkbox(
                "Show Query Details",
                value=st.session_state.get('show_query_details', False),
                key="show_details_setting"
            )
            
            settings['enable_query_suggestions'] = st.checkbox(
                "Enable Query Suggestions",
                value=st.session_state.get('enable_query_suggestions', True),
                key="query_suggestions_setting"
            )
            
            # Performance settings
            st.markdown("**Performance**")
            settings['cache_enabled'] = st.checkbox(
                "Enable Caching",
                value=st.session_state.get('cache_enabled', True),
                key="cache_setting"
            )
            
            settings['parallel_execution'] = st.checkbox(
                "Parallel Query Execution",
                value=st.session_state.get('parallel_execution', False),
                key="parallel_setting"
            )
        
        return settings
    
    @staticmethod
    def render_query_input(placeholder: str = "What would you like to know about your data?") -> str:
        """
        Render the main query input interface
        
        Returns:
            str: User query input
        """
        st.markdown("### 💬 Ask Your Database")
        
        # Query input area
        user_query = st.text_area(
            "Enter your question:",
            placeholder=placeholder,
            height=100,
            key="main_query_input",
            help="Ask questions in natural language, e.g., 'Show me top 10 products by price' or 'Create a chart of sales by month'"
        )
        
        return user_query
    
    @staticmethod
    def render_query_buttons(user_query: str, query_suggestions: List[str] = None) -> str:
        """
        Render query action buttons
        
        Returns:
            str: Action taken ('execute', 'example', 'clear', or '')
        """
        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
        
        action = ""
        
        with col1:
            if st.button(
                "🔍 Execute Query",
                type="primary",
                disabled=not user_query.strip(),
                use_container_width=True
            ):
                action = "execute"
        
        with col2:
            if st.button("🎲 Example", use_container_width=True):
                action = "example"
        
        with col3:
            if st.button("💡 Suggest", use_container_width=True):
                action = "suggest"
        
        with col4:
            if st.button("🗑️ Clear", use_container_width=True):
                action = "clear"
        
        return action
    
    @staticmethod
    def render_query_suggestions(suggestions: List[str]) -> Optional[str]:
        """
        Render query suggestions
        
        Returns:
            str: Selected suggestion or None
        """
        if not suggestions:
            return None
        
        st.markdown("### 💡 Query Suggestions")
        
        selected_suggestion = None
        
        # Display suggestions in columns
        cols = st.columns(2)
        for i, suggestion in enumerate(suggestions[:6]):  # Show max 6 suggestions
            with cols[i % 2]:
                if st.button(
                    suggestion,
                    key=f"suggestion_{i}",
                    use_container_width=True
                ):
                    selected_suggestion = suggestion
        
        return selected_suggestion
    
    @staticmethod
    def render_chat_message(role: str, content: str, avatar: str = None) -> None:
        """Render a chat message"""
        with st.chat_message(role, avatar=avatar):
            st.markdown(content)
    
    @staticmethod
    def render_query_result(result: Dict[str, Any]) -> None:
        """Render query execution result"""
        if result.get('success'):
            # Success message
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "Execution Time",
                    f"{result.get('execution_time', 0):.2f}s"
                )
            
            with col2:
                st.metric(
                    "Results",
                    result.get('result_count', 0)
                )
            
            with col3:
                st.metric(
                    "Status",
                    "✅ Success"
                )
            
            # Show results if available
            if result.get('results'):
                with st.expander("📊 Raw Results", expanded=False):
                    if isinstance(result['results'], list) and len(result['results']) > 0:
                        # Show as dataframe if possible
                        try:
                            import pandas as pd
                            df = pd.DataFrame(result['results'])
                            st.dataframe(df, use_container_width=True)
                        except:
                            # Fallback to JSON
                            st.json(result['results'])
                    else:
                        st.write(result['results'])
        
        else:
            # Error message
            st.error(f"❌ Query failed: {result.get('error', 'Unknown error')}")
            
            if result.get('execution_time'):
                st.caption(f"Failed after {result['execution_time']:.2f}s")
    
    @staticmethod
    def render_loading_spinner(message: str = "Processing...") -> None:
        """Render loading spinner with message"""
        with st.spinner(message):
            time.sleep(0.1)  # Small delay to ensure spinner shows
    
    @staticmethod
    def render_info_card(title: str, content: str, icon: str = "ℹ️") -> None:
        """Render an information card"""
        st.markdown(f"""
        <div style="
            background: #f0f2f6;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #1f4e79;
            margin: 1rem 0;
        ">
            <h4>{icon} {title}</h4>
            <p>{content}</p>
        </div>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def render_stats_dashboard(stats: Dict[str, Any]) -> None:
        """Render statistics dashboard"""
        st.markdown("### 📊 Session Statistics")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Total Queries",
                stats.get('total_queries', 0)
            )
        
        with col2:
            st.metric(
                "Success Rate",
                f"{stats.get('success_rate', 0):.1%}"
            )
        
        with col3:
            st.metric(
                "Avg Response Time",
                f"{stats.get('avg_response_time', 0):.2f}s"
            )
        
        with col4:
            st.metric(
                "Visualizations",
                stats.get('visualizations_created', 0)
            )
