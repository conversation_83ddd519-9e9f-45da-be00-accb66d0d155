version: '3.8'

services:
  datapilot:
    build: .
    ports:
      - "8501:8501"
    environment:
      - MONGODB_URI=${MONGODB_URI}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - DEBUG=true
    volumes:
      - .:/app
      - /app/__pycache__
    restart: unless-stopped
    depends_on:
      - mongodb
    networks:
      - datapilot-network

  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=datapilot
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    restart: unless-stopped
    networks:
      - datapilot-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - datapilot-network
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
  redis_data:

networks:
  datapilot-network:
    driver: bridge
