"""
Metadata Storage & Vector Indexing Engine
Stores discovered schemas with vector embeddings for intelligent retrieval
"""

import asyncio
import json
import pickle
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
import logging
import os
import sqlite3
import numpy as np
from pathlib import Path

# For vector embeddings (we'll use a simple approach first, can upgrade to OpenAI later)
from collections import defaultdict
import re

from schema_discovery_engine import CollectionSchema, FieldAnalysis, FieldSemanticType

logger = logging.getLogger(__name__)

@dataclass
class SchemaEmbedding:
    """Vector embedding for schema elements"""
    schema_id: str
    element_type: str  # 'collection', 'field', 'relationship'
    element_name: str
    embedding: List[float]
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class QueryContext:
    """Context for natural language queries"""
    query: str
    intent: str  # 'find', 'count', 'aggregate', 'update', etc.
    entities: List[str] = field(default_factory=list)
    collections: List[str] = field(default_factory=list)
    fields: List[str] = field(default_factory=list)
    confidence: float = 0.0

class MetadataStorageEngine:
    """
    Metadata storage engine that:
    1. Stores discovered schemas in SQLite
    2. Creates vector embeddings for semantic search
    3. Provides intelligent schema retrieval
    4. Supports natural language to schema mapping
    """
    
    def __init__(self, storage_path: str = "metadata_storage"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # Database paths
        self.db_path = self.storage_path / "schemas.db"
        self.embeddings_path = self.storage_path / "embeddings.pkl"
        
        # In-memory caches
        self.schema_cache: Dict[str, CollectionSchema] = {}
        self.embeddings_cache: Dict[str, SchemaEmbedding] = {}
        self.field_index: Dict[str, List[str]] = defaultdict(list)  # field_name -> [schema_ids]
        self.semantic_index: Dict[str, List[str]] = defaultdict(list)  # semantic_type -> [field_ids]
        
        # Simple vocabulary for embeddings (can be replaced with OpenAI embeddings)
        self.vocabulary = set()
        self.word_to_idx = {}
        
        # Initialize storage
        self._initialize_storage()
        
    def _initialize_storage(self):
        """Initialize SQLite database and load existing data"""
        try:
            # Create database tables
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS schemas (
                        id TEXT PRIMARY KEY,
                        database_name TEXT,
                        collection_name TEXT,
                        schema_data TEXT,
                        confidence REAL,
                        business_context TEXT,
                        created_at TIMESTAMP,
                        updated_at TIMESTAMP
                    )
                """)
                
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS fields (
                        id TEXT PRIMARY KEY,
                        schema_id TEXT,
                        field_name TEXT,
                        data_type TEXT,
                        semantic_type TEXT,
                        frequency REAL,
                        uniqueness REAL,
                        confidence REAL,
                        sample_values TEXT,
                        FOREIGN KEY (schema_id) REFERENCES schemas (id)
                    )
                """)
                
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS relationships (
                        id TEXT PRIMARY KEY,
                        source_schema TEXT,
                        target_schema TEXT,
                        relationship_type TEXT,
                        field_name TEXT,
                        confidence REAL,
                        created_at TIMESTAMP
                    )
                """)
                
                # Create indexes for fast lookup
                conn.execute("CREATE INDEX IF NOT EXISTS idx_schemas_collection ON schemas(database_name, collection_name)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_fields_name ON fields(field_name)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_fields_semantic ON fields(semantic_type)")
            
            # Load existing embeddings
            self._load_embeddings()
            
            logger.info("Metadata storage initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing metadata storage: {str(e)}")
            raise

    def store_schema(self, schema: CollectionSchema) -> str:
        """Store a discovered schema with vector embeddings"""
        try:
            schema_id = self._generate_schema_id(schema.database, schema.collection)
            
            # Store in database
            with sqlite3.connect(self.db_path) as conn:
                # Store schema
                conn.execute("""
                    INSERT OR REPLACE INTO schemas 
                    (id, database_name, collection_name, schema_data, confidence, 
                     business_context, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    schema_id,
                    schema.database,
                    schema.collection,
                    json.dumps(asdict(schema), default=str),
                    schema.schema_confidence,
                    schema.business_context,
                    schema.last_analyzed,
                    datetime.now()
                ))
                
                # Store fields
                for field_name, field_analysis in schema.fields.items():
                    field_id = f"{schema_id}.{field_name}"
                    conn.execute("""
                        INSERT OR REPLACE INTO fields
                        (id, schema_id, field_name, data_type, semantic_type,
                         frequency, uniqueness, confidence, sample_values)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        field_id,
                        schema_id,
                        field_name,
                        field_analysis.data_type,
                        field_analysis.semantic_type.value,
                        field_analysis.frequency,
                        field_analysis.uniqueness,
                        field_analysis.confidence_score,
                        json.dumps(field_analysis.sample_values[:5], default=str)
                    ))
            
            # Create and store embeddings
            self._create_schema_embeddings(schema_id, schema)
            
            # Update caches
            self.schema_cache[schema_id] = schema
            self._update_indexes(schema_id, schema)
            
            logger.info(f"Schema stored: {schema_id}")
            return schema_id
            
        except Exception as e:
            logger.error(f"Error storing schema: {str(e)}")
            raise

    def _generate_schema_id(self, database: str, collection: str) -> str:
        """Generate unique schema ID"""
        return f"{database}.{collection}"

    def _create_schema_embeddings(self, schema_id: str, schema: CollectionSchema):
        """Create vector embeddings for schema elements"""
        try:
            # Collection-level embedding
            collection_text = self._create_collection_description(schema)
            collection_embedding = self._create_simple_embedding(collection_text)
            
            self.embeddings_cache[f"{schema_id}.collection"] = SchemaEmbedding(
                schema_id=schema_id,
                element_type="collection",
                element_name=schema.collection,
                embedding=collection_embedding,
                metadata={
                    "database": schema.database,
                    "business_context": schema.business_context,
                    "document_count": schema.document_count
                }
            )
            
            # Field-level embeddings
            for field_name, field_analysis in schema.fields.items():
                field_text = self._create_field_description(field_name, field_analysis)
                field_embedding = self._create_simple_embedding(field_text)
                
                self.embeddings_cache[f"{schema_id}.{field_name}"] = SchemaEmbedding(
                    schema_id=schema_id,
                    element_type="field",
                    element_name=field_name,
                    embedding=field_embedding,
                    metadata={
                        "data_type": field_analysis.data_type,
                        "semantic_type": field_analysis.semantic_type.value,
                        "frequency": field_analysis.frequency,
                        "sample_values": field_analysis.sample_values[:3]
                    }
                )
            
            # Save embeddings to disk
            self._save_embeddings()
            
        except Exception as e:
            logger.error(f"Error creating embeddings for {schema_id}: {str(e)}")

    def _create_collection_description(self, schema: CollectionSchema) -> str:
        """Create natural language description of collection"""
        field_names = list(schema.fields.keys())
        semantic_types = [f.semantic_type.value for f in schema.fields.values()]
        
        description = f"""
        Collection {schema.collection} in database {schema.database}.
        Business context: {schema.business_context}.
        Contains {schema.document_count} documents.
        Fields: {', '.join(field_names[:10])}.
        Data types: {', '.join(set(semantic_types))}.
        """
        return description.strip()

    def _create_field_description(self, field_name: str, field_analysis: FieldAnalysis) -> str:
        """Create natural language description of field"""
        sample_str = ', '.join(str(v) for v in field_analysis.sample_values[:3])
        
        description = f"""
        Field {field_name} of type {field_analysis.data_type}.
        Semantic type: {field_analysis.semantic_type.value}.
        Frequency: {field_analysis.frequency:.2f}.
        Sample values: {sample_str}.
        """
        return description.strip()

    def _create_simple_embedding(self, text: str) -> List[float]:
        """Create simple TF-IDF style embedding (can be replaced with OpenAI)"""
        # Simple word-based embedding
        words = re.findall(r'\w+', text.lower())
        
        # Update vocabulary
        self.vocabulary.update(words)
        
        # Create word index if needed
        if not self.word_to_idx:
            self.word_to_idx = {word: idx for idx, word in enumerate(sorted(self.vocabulary))}
        
        # Create embedding vector
        embedding_size = max(100, len(self.word_to_idx))  # Minimum 100 dimensions
        embedding = [0.0] * embedding_size
        
        word_counts = defaultdict(int)
        for word in words:
            word_counts[word] += 1
        
        # Simple TF-IDF approximation
        for word, count in word_counts.items():
            if word in self.word_to_idx:
                idx = self.word_to_idx[word] % embedding_size
                embedding[idx] = count / len(words)  # Term frequency
        
        return embedding

    def _update_indexes(self, schema_id: str, schema: CollectionSchema):
        """Update in-memory indexes for fast lookup"""
        # Field name index
        for field_name in schema.fields.keys():
            self.field_index[field_name.lower()].append(schema_id)
        
        # Semantic type index
        for field_analysis in schema.fields.values():
            semantic_type = field_analysis.semantic_type.value
            self.semantic_index[semantic_type].append(f"{schema_id}.{field_analysis.name}")

    def find_relevant_schemas(self, query: str, limit: int = 5) -> List[Tuple[str, float]]:
        """Find schemas relevant to a natural language query"""
        try:
            query_embedding = self._create_simple_embedding(query)
            
            # Calculate similarities
            similarities = []
            for embedding_id, schema_embedding in self.embeddings_cache.items():
                if schema_embedding.element_type == "collection":
                    similarity = self._calculate_similarity(query_embedding, schema_embedding.embedding)
                    similarities.append((schema_embedding.schema_id, similarity))
            
            # Sort by similarity and return top results
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:limit]
            
        except Exception as e:
            logger.error(f"Error finding relevant schemas: {str(e)}")
            return []

    def find_relevant_fields(self, query: str, schema_ids: List[str] = None, limit: int = 10) -> List[Tuple[str, str, float]]:
        """Find fields relevant to a query"""
        try:
            query_embedding = self._create_simple_embedding(query)
            
            # Calculate similarities for fields
            similarities = []
            for embedding_id, field_embedding in self.embeddings_cache.items():
                if field_embedding.element_type == "field":
                    # Filter by schema if specified
                    if schema_ids and field_embedding.schema_id not in schema_ids:
                        continue
                    
                    similarity = self._calculate_similarity(query_embedding, field_embedding.embedding)
                    similarities.append((
                        field_embedding.schema_id,
                        field_embedding.element_name,
                        similarity
                    ))
            
            # Sort by similarity
            similarities.sort(key=lambda x: x[2], reverse=True)
            return similarities[:limit]
            
        except Exception as e:
            logger.error(f"Error finding relevant fields: {str(e)}")
            return []

    def _calculate_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Ensure same length
            min_len = min(len(vec1), len(vec2))
            v1 = np.array(vec1[:min_len])
            v2 = np.array(vec2[:min_len])
            
            # Calculate cosine similarity
            dot_product = np.dot(v1, v2)
            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception:
            return 0.0

    def get_schema_by_id(self, schema_id: str) -> Optional[CollectionSchema]:
        """Retrieve schema by ID"""
        if schema_id in self.schema_cache:
            return self.schema_cache[schema_id]
        
        # Load from database
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT schema_data FROM schemas WHERE id = ?",
                    (schema_id,)
                )
                row = cursor.fetchone()
                if row:
                    schema_data = json.loads(row[0])
                    # Convert back to CollectionSchema object (simplified)
                    return schema_data
        except Exception as e:
            logger.error(f"Error loading schema {schema_id}: {str(e)}")
        
        return None

    def get_field_suggestions(self, partial_name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get field suggestions based on partial name"""
        suggestions = []
        partial_lower = partial_name.lower()
        
        for field_name, schema_ids in self.field_index.items():
            if partial_lower in field_name:
                for schema_id in schema_ids:
                    suggestions.append({
                        'field_name': field_name,
                        'schema_id': schema_id,
                        'collection': schema_id.split('.')[-1]
                    })
        
        return suggestions[:limit]

    def _save_embeddings(self):
        """Save embeddings to disk"""
        try:
            with open(self.embeddings_path, 'wb') as f:
                pickle.dump({
                    'embeddings': self.embeddings_cache,
                    'vocabulary': self.vocabulary,
                    'word_to_idx': self.word_to_idx
                }, f)
        except Exception as e:
            logger.error(f"Error saving embeddings: {str(e)}")

    def _load_embeddings(self):
        """Load embeddings from disk"""
        try:
            if self.embeddings_path.exists():
                with open(self.embeddings_path, 'rb') as f:
                    data = pickle.load(f)
                    self.embeddings_cache = data.get('embeddings', {})
                    self.vocabulary = data.get('vocabulary', set())
                    self.word_to_idx = data.get('word_to_idx', {})
                logger.info(f"Loaded {len(self.embeddings_cache)} embeddings")
        except Exception as e:
            logger.error(f"Error loading embeddings: {str(e)}")

    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                schema_count = conn.execute("SELECT COUNT(*) FROM schemas").fetchone()[0]
                field_count = conn.execute("SELECT COUNT(*) FROM fields").fetchone()[0]
                
            return {
                'schemas_stored': schema_count,
                'fields_indexed': field_count,
                'embeddings_cached': len(self.embeddings_cache),
                'vocabulary_size': len(self.vocabulary),
                'storage_path': str(self.storage_path)
            }
        except Exception as e:
            logger.error(f"Error getting storage stats: {str(e)}")
            return {}
