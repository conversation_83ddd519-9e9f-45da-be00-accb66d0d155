"""
Test script for Enhanced MongoDB Connection Manager
Demonstrates multi-database discovery and schema analysis
"""

import asyncio
import json
from datetime import datetime
from mongodb_connection_manager import EnhancedMongoConnectionManager

async def test_connection_manager():
    """Test the enhanced MongoDB connection manager"""
    
    print("🚀 Testing Enhanced MongoDB Connection Manager")
    print("=" * 60)
    
    # Initialize connection manager
    manager = EnhancedMongoConnectionManager()
    
    try:
        # Test connection
        print("\n1️⃣ Testing Connection...")
        connected = await manager.connect()
        
        if not connected:
            print("❌ Failed to connect to MongoDB")
            return
        
        # Get cluster summary
        print("\n2️⃣ Cluster Summary:")
        summary = manager.get_cluster_summary()
        print(json.dumps(summary, indent=2, default=str))
        
        # List databases
        print("\n3️⃣ Available Databases:")
        databases = manager.get_database_list()
        for db in databases:
            print(f"   📁 {db}")
        
        # List collections for each database
        print("\n4️⃣ Collections by Database:")
        for db in databases:
            collections = manager.get_collection_list(db)
            print(f"   📁 {db}:")
            for col in collections:
                print(f"      📄 {col}")
        
        # Show collection metadata
        print("\n5️⃣ Collection Metadata Examples:")
        for db in databases[:2]:  # Show first 2 databases
            collections = manager.get_collection_list(db)
            for col in collections[:2]:  # Show first 2 collections
                metadata = manager.get_collection_metadata(db, col)
                if metadata:
                    print(f"\n   📊 {db}.{col}:")
                    print(f"      Documents: {metadata.document_count}")
                    print(f"      Indexes: {len(metadata.indexes)}")
                    print(f"      Field Types: {metadata.field_types}")
                    if metadata.schema_sample:
                        print(f"      Sample Fields: {list(metadata.schema_sample.keys())}")
        
        # Test query routing (placeholder)
        print("\n6️⃣ Connection Status:")
        print(f"   Status: {manager.status.value}")
        print(f"   Total Databases: {len(databases)}")
        print(f"   Total Collections: {len(manager.collections)}")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        manager.close_connection()
        print("\n✅ Connection closed")

async def test_specific_database_analysis():
    """Test detailed analysis of a specific database"""
    
    print("\n" + "=" * 60)
    print("🔬 Detailed Database Analysis")
    print("=" * 60)
    
    manager = EnhancedMongoConnectionManager()
    
    try:
        await manager.connect()
        
        databases = manager.get_database_list()
        if not databases:
            print("No databases found for detailed analysis")
            return
        
        # Analyze first database in detail
        target_db = databases[0]
        print(f"\n🎯 Analyzing database: {target_db}")
        
        collections = manager.get_collection_list(target_db)
        print(f"Collections found: {len(collections)}")
        
        for col in collections:
            metadata = manager.get_collection_metadata(target_db, col)
            if metadata:
                print(f"\n📊 Collection: {col}")
                print(f"   📈 Documents: {metadata.document_count:,}")
                print(f"   🔍 Indexes: {len(metadata.indexes)}")
                
                # Show index details
                for idx in metadata.indexes:
                    idx_name = idx.get('name', 'unknown')
                    idx_keys = idx.get('key', {})
                    print(f"      🔑 {idx_name}: {list(idx_keys.keys())}")
                
                # Show field analysis
                if metadata.field_types:
                    print(f"   📋 Fields ({len(metadata.field_types)}):")
                    for field, field_type in list(metadata.field_types.items())[:5]:
                        print(f"      • {field}: {field_type}")
                    if len(metadata.field_types) > 5:
                        print(f"      ... and {len(metadata.field_types) - 5} more fields")
    
    except Exception as e:
        print(f"❌ Error during detailed analysis: {str(e)}")
    
    finally:
        manager.close_connection()

def run_tests():
    """Run all tests"""
    print("🧪 Starting MongoDB Connection Manager Tests")
    print(f"⏰ Started at: {datetime.now()}")
    
    # Run basic tests
    asyncio.run(test_connection_manager())
    
    # Run detailed analysis
    asyncio.run(test_specific_database_analysis())
    
    print(f"\n⏰ Completed at: {datetime.now()}")
    print("🎉 All tests completed!")

if __name__ == "__main__":
    run_tests()
